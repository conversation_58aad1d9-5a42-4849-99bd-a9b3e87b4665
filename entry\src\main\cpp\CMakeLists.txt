# the minimum version of CMake.
cmake_minimum_required(VERSION 3.5.0)
project(MyApplication)

set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

add_definitions(-DOHOS_PLATFORM)

if(DEFINED PACKAGE_FIND_FILE)
    include(${PACKAGE_FIND_FILE})
endif()

# 包含路径配置
include_directories(${NATIVERENDER_ROOT_PATH}
                    ${NATIVERENDER_ROOT_PATH}/include
                    ${NATIVERENDER_ROOT_PATH}/include/scintilla
                    ${NATIVERENDER_ROOT_PATH}/include/lexilla
                    ${NATIVERENDER_ROOT_PATH}/src/scintilla
                    ${NATIVERENDER_ROOT_PATH}/src/lexilla/lexlib)

# 收集 Scintilla 源文件
file(GLOB SCINTILLA_SOURCES
    "src/scintilla/*.cxx"
    "src/scintilla/*.cpp"
)

# 收集 Lexilla 源文件
file(GLOB LEXILLA_SOURCES
    "src/lexilla/src/*.cxx"
    "src/lexilla/lexlib/*.cxx"
    "src/lexilla/lexers/*.cxx"
)

# 鸿蒙平台适配源文件
set(HARMONY_SOURCES
    src/scintilla_harmony.cpp
    src/surface_harmony.cpp
    src/surface_harmony_ext.cpp
    src/surface_harmony_missing.cpp
)

add_library(entry SHARED
 napi_init.cpp
 surface/Surface_Core.cpp
 manager/plugin_manager.cpp
 ${SCINTILLA_SOURCES}
 ${LEXILLA_SOURCES}
 ${HARMONY_SOURCES}
)

find_library(
    # Sets the name of the path variable.
    EGL-lib
    # Specifies the name of the NDK library that
    # you want CMake to locate.
    EGL
)

find_library(
    # Sets the name of the path variable.
    GLES-lib
    # Specifies the name of the NDK library that
    # you want CMake to locate.
    GLESv3
)

find_library(
    # Sets the name of the path variable.
    hilog-lib
    # Specifies the name of the NDK library that
    # you want CMake to locate.
    hilog_ndk.z
)

find_library(
    # Sets the name of the path variable.
    libace-lib
    # Specifies the name of the NDK library that
    # you want CMake to locate.
    ace_ndk.z
)

find_library(
    # Sets the name of the path variable.
    libnapi-lib
    # Specifies the name of the NDK library that
    # you want CMake to locate.
    ace_napi.z
)

find_library(
    # Sets the name of the path variable.
    libuv-lib
    # Specifies the name of the NDK library that
    # you want CMake to locate.
    uv
)

find_library(
    # Sets the name of the path variable.
    drawing-lib
    # Specifies the name of the NDK library that
    # you want CMake to locate.
    native_drawing
)

# 设置编译选项
set_target_properties(entry PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 添加编译定义
target_compile_definitions(entry PRIVATE
    OHOS_PLATFORM=1
    SCI_LEXER=1
    _CRT_SECURE_NO_DEPRECATE=1
)

target_link_libraries(entry PUBLIC
 ${EGL-lib} ${GLES-lib} ${hilog-lib} ${libace-lib} ${libnapi-lib} ${libuv-lib} ${drawing-lib} libnative_window.so)