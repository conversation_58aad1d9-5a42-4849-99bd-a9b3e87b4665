{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "C:\\Users\\<USER>\\.ohos\\config\\default_notepad-next_MIl2oCDzP29LP1yD6Cz7ssrfneCMSkVq-QUZQE1mZsQ=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B9D44717600A0A294F15F0FF5585F1A451B01A5B175B0963237FD0F3C4FE73C842DDBF83BB2CA2C",
          "profile": "C:\\Users\\<USER>\\.ohos\\config\\default_notepad-next_MIl2oCDzP29LP1yD6Cz7ssrfneCMSkVq-QUZQE1mZsQ=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\default_notepad-next_MIl2oCDzP29LP1yD6Cz7ssrfneCMSkVq-QUZQE1mZsQ=.p12",
          "storePassword": "0000001B551EA1680F608354040BEEFDE0AD0FB1EEC52EB03C5363CC220A7A53FCD25CC3FD92FD4C2A319F"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.4(16)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}