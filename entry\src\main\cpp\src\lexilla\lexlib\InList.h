// Scintilla source code edit control
/** @file InList.h
 ** Check if a string is in a list.
 **/
// Copyright 2024 by <PERSON> <<EMAIL>>
// The License.txt file describes the conditions under which this software may be distributed.

#ifndef INLIST_H
#define INLIST_H
#include <cassert>

#include <string>
#include <string_view>
#include <initializer_list>

#include "InList.h"

namespace Lexilla {

bool InList(std::string_view value, std::initializer_list<std::string_view> list) noexcept;
bool InListCaseInsensitive(std::string_view value, std::initializer_list<std::string_view> list) noexcept;

}

#endif
