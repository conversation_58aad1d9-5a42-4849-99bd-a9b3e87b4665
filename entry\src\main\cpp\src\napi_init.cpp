#include "napi/native_api.h"
#include "include/scintilla_harmony.h"
#include <hilog/log.h>
#include <unordered_map>

#define LOG_PRINT_DOMAIN 0xFF00
#define LOG_TAG "ScintillaNAPI"

using namespace Scintilla::Internal;

// 全局编辑器实例映射
static std::unordered_map<int64_t, ScintillaHarmony*> g_editorMap;
static int64_t g_nextEditorId = 1;

static napi_value Add(napi_env env, napi_callback_info info)
{
    size_t argc = 2;
    napi_value args[2] = {nullptr};

    napi_get_cb_info(env, info, &argc, args , nullptr, nullptr);

    napi_valuetype valuetype0;
    napi_typeof(env, args[0], &valuetype0);

    napi_valuetype valuetype1;
    napi_typeof(env, args[1], &valuetype1);

    double value0;
    napi_get_value_double(env, args[0], &value0);

    double value1;
    napi_get_value_double(env, args[1], &value1);

    napi_value sum;
    napi_create_double(env, value0 + value1, &sum);

    return sum;
}

// 创建编辑器实例
static napi_value CreateEditor(napi_env env, napi_callback_info info) {
    try {
        // 创建ScintillaHarmony实例
        ScintillaHarmony* editor = new ScintillaHarmony();

        // 生成唯一ID
        int64_t editorId = g_nextEditorId++;
        g_editorMap[editorId] = editor;

        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Created editor with ID: %{public}lld", editorId);

        // 返回编辑器ID
        napi_value result;
        napi_create_int64(env, editorId, &result);
        return result;

    } catch (const std::exception& e) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Failed to create editor: %{public}s", e.what());
        napi_value result;
        napi_create_int64(env, -1, &result);
        return result;
    }
}

// 设置文本
static napi_value SetText(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 2) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "SetText: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = g_editorMap.find(editorId);
    if (it == g_editorMap.end()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "SetText: editor not found");
        return nullptr;
    }

    ScintillaHarmony* editor = it->second;

    // 获取文本
    char text[1024];
    size_t textLen;
    napi_get_value_string_utf8(env, args[1], text, sizeof(text), &textLen);

    // 设置文本
    editor->SetText(text);

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Set text: %{public}s", text);

    return nullptr;
}

// 插入文本
static napi_value InsertText(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 2) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "InsertText: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = g_editorMap.find(editorId);
    if (it == g_editorMap.end()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "InsertText: editor not found");
        return nullptr;
    }

    ScintillaHarmony* editor = it->second;

    // 获取文本
    char text[1024];
    size_t textLen;
    napi_get_value_string_utf8(env, args[1], text, sizeof(text), &textLen);

    // 插入文本
    Sci::Position pos = editor->CurrentPosition();
    editor->InsertString(pos, text, textLen);

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Inserted text: %{public}s", text);

    return nullptr;
}

// 获取文本
static napi_value GetText(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "GetText: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = g_editorMap.find(editorId);
    if (it == g_editorMap.end()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "GetText: editor not found");
        return nullptr;
    }

    ScintillaHarmony* editor = it->second;

    // 获取文档内容
    Sci::Position len = editor->Length();
    char* buffer = new char[len + 1];
    editor->GetText(len + 1, buffer);
    buffer[len] = '\0';

    napi_value result;
    napi_create_string_utf8(env, buffer, len, &result);

    delete[] buffer;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Retrieved text length: %{public}lld", len);

    return result;
}

// 设置样式
static napi_value SetStyle(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value args[4];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 4) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "SetStyle: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = g_editorMap.find(editorId);
    if (it == g_editorMap.end()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "SetStyle: editor not found");
        return nullptr;
    }

    ScintillaHarmony* editor = it->second;

    int32_t start, end, style;
    napi_get_value_int32(env, args[1], &start);
    napi_get_value_int32(env, args[2], &end);
    napi_get_value_int32(env, args[3], &style);

    // 设置样式
    editor->StartStyling(start);
    editor->SetStyling(end - start, style);

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Set style: start=%{public}d, end=%{public}d, style=%{public}d",
                 start, end, style);

    return nullptr;
}

// 初始化编辑器与XComponent
static napi_value InitEditor(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 2) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "InitEditor: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = g_editorMap.find(editorId);
    if (it == g_editorMap.end()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "InitEditor: editor not found");
        return nullptr;
    }

    ScintillaHarmony* editor = it->second;

    // 获取XComponent指针
    OH_NativeXComponent* component;
    napi_get_value_external(env, args[1], (void**)&component);

    if (component) {
        // 这里需要获取window指针，暂时传nullptr
        editor->Init(component, nullptr);
        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Editor initialized with XComponent");
    }

    return nullptr;
}

// 设置Canvas
static napi_value SetCanvas(napi_env env, napi_callback_info info) {
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 2) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "SetCanvas: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = g_editorMap.find(editorId);
    if (it == g_editorMap.end()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "SetCanvas: editor not found");
        return nullptr;
    }

    ScintillaHarmony* editor = it->second;

    // 获取Canvas指针
    void* canvas;
    napi_get_value_external(env, args[1], &canvas);

    if (canvas) {
        editor->SetCanvas(canvas);
        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Canvas set for editor: %{public}lld", editorId);
    }

    return nullptr;
}

// 销毁编辑器
static napi_value DestroyEditor(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "DestroyEditor: insufficient arguments");
        return nullptr;
    }

    // 获取编辑器ID
    int64_t editorId;
    napi_get_value_int64(env, args[0], &editorId);

    auto it = g_editorMap.find(editorId);
    if (it != g_editorMap.end()) {
        delete it->second;
        g_editorMap.erase(it);
        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Destroyed editor with ID: %{public}lld", editorId);
    }

    return nullptr;
}

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports)
{
    napi_property_descriptor desc[] = {
        { "add", nullptr, Add, nullptr, nullptr, nullptr, napi_default, nullptr },
        {"createEditor", nullptr, CreateEditor, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setText", nullptr, SetText, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"insertText", nullptr, InsertText, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"getText", nullptr, GetText, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setStyle", nullptr, SetStyle, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"initEditor", nullptr, InitEditor, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"setCanvas", nullptr, SetCanvas, nullptr, nullptr, nullptr, napi_default, nullptr},
        {"destroyEditor", nullptr, DestroyEditor, nullptr, nullptr, nullptr, napi_default, nullptr}
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "entry",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

extern "C" __attribute__((constructor)) void RegisterEntryModule(void)
{
    napi_module_register(&demoModule);
}
