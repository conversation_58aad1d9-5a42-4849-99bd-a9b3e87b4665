import EnumBar from 'ets/layout/EnumBar'
import ToolsBar from 'ets/layout/ToolsBar'
import Content from  'ets/layout/Content'
@Entry
@Component
struct Index {
  @State showToolsBar:Boolean = false
  @State showEnumBar:Boolean = true

  build() {
    Column() {
      // if(this.showEnumBar){
      //   EnumBar()
      // }
      // if(this.showToolsBar){
      //   ToolsBar()
      // }
      Content()
    }
    .width('100%')
    .height('100%')
  }
}
