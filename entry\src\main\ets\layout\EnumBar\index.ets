import FileManager from  'ets/layout/EnumBar/EnumItem/FileManager'
import WritingLanguage from 'ets/layout/EnumBar/EnumItem/WritingLanguage'
import { LengthUnit } from '@kit.ArkUI'

@ComponentV2
export default struct EnumBar{

  // 编写语言列表
  @Local SelectOption:SelectOption[] = [
    {
      value:'Text',
    },
    {
      value:'Java',

    },
    {
      value:'JavaScript',
    },
    {
      value:'C',
    },
    {
      value:'C++',
    }
  ]
  build() {
    Flex({space:{main:{value:20,unit:LengthUnit.PX}}}){
      FileManager()
      WritingLanguage({selectDatasource: this.SelectOption})
    }
    .padding({left:10,top:20,bottom:20,right:10})
    .align(Alignment.Center)
    .border({width:'1vp',color:Color.Black,radius:'2vp'})
  }
}