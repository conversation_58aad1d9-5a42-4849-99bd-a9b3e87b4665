// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00D544CC992062D2E3CD4BF6 /* LexGDScript.cxx in Sources */ = {isa = PBXBuildFile; fileRef = A383409E9A994F461550FEC1 /* LexGDScript.cxx */; };
		283639BC268FD4EA009D58A1 /* LexAccessor.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 283639BB268FD4EA009D58A1 /* LexAccessor.cxx */; };
		283A17AE2B47E61100DF5C82 /* InList.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 283A17AC2B47E61100DF5C82 /* InList.cxx */; };
		283A17AF2B47E61100DF5C82 /* InList.h in Headers */ = {isa = PBXBuildFile; fileRef = 283A17AD2B47E61100DF5C82 /* InList.h */; };
		28BA72AB24E34D5B00272C2D /* LexerBase.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA728F24E34D5A00272C2D /* LexerBase.cxx */; };
		28BA72AC24E34D5B00272C2D /* LexAccessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729024E34D5A00272C2D /* LexAccessor.h */; };
		28BA72AD24E34D5B00272C2D /* DefaultLexer.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729124E34D5A00272C2D /* DefaultLexer.h */; };
		28BA72AE24E34D5B00272C2D /* SubStyles.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729224E34D5A00272C2D /* SubStyles.h */; };
		28BA72B024E34D5B00272C2D /* LexerModule.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729424E34D5A00272C2D /* LexerModule.h */; };
		28BA72B124E34D5B00272C2D /* CharacterCategory.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA729524E34D5A00272C2D /* CharacterCategory.cxx */; };
		28BA72B224E34D5B00272C2D /* LexerSimple.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729624E34D5A00272C2D /* LexerSimple.h */; };
		28BA72B324E34D5B00272C2D /* Accessor.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729724E34D5A00272C2D /* Accessor.h */; };
		28BA72B424E34D5B00272C2D /* PropSetSimple.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA729824E34D5A00272C2D /* PropSetSimple.cxx */; };
		28BA72B524E34D5B00272C2D /* CharacterSet.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA729924E34D5A00272C2D /* CharacterSet.cxx */; };
		28BA72B624E34D5B00272C2D /* SparseState.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729A24E34D5A00272C2D /* SparseState.h */; };
		28BA72B724E34D5B00272C2D /* WordList.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729B24E34D5A00272C2D /* WordList.h */; };
		28BA72B824E34D5B00272C2D /* DefaultLexer.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA729C24E34D5A00272C2D /* DefaultLexer.cxx */; };
		28BA72BA24E34D5B00272C2D /* WordList.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA729E24E34D5A00272C2D /* WordList.cxx */; };
		28BA72BB24E34D5B00272C2D /* OptionSet.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA729F24E34D5A00272C2D /* OptionSet.h */; };
		28BA72BC24E34D5B00272C2D /* CatalogueModules.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA72A024E34D5B00272C2D /* CatalogueModules.h */; };
		28BA72BD24E34D5B00272C2D /* CharacterSet.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA72A124E34D5B00272C2D /* CharacterSet.h */; };
		28BA72BE24E34D5B00272C2D /* StyleContext.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA72A224E34D5B00272C2D /* StyleContext.h */; };
		28BA72BF24E34D5B00272C2D /* PropSetSimple.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA72A324E34D5B00272C2D /* PropSetSimple.h */; };
		28BA72C024E34D5B00272C2D /* StringCopy.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA72A424E34D5B00272C2D /* StringCopy.h */; };
		28BA72C124E34D5B00272C2D /* LexerModule.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72A524E34D5B00272C2D /* LexerModule.cxx */; };
		28BA72C224E34D5B00272C2D /* LexerBase.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA72A624E34D5B00272C2D /* LexerBase.h */; };
		28BA72C324E34D5B00272C2D /* LexerSimple.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72A724E34D5B00272C2D /* LexerSimple.cxx */; };
		28BA72C424E34D5B00272C2D /* StyleContext.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72A824E34D5B00272C2D /* StyleContext.cxx */; };
		28BA72C524E34D5B00272C2D /* CharacterCategory.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA72A924E34D5B00272C2D /* CharacterCategory.h */; };
		28BA72C624E34D5B00272C2D /* Accessor.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72AA24E34D5B00272C2D /* Accessor.cxx */; };
		28BA733924E34D9700272C2D /* LexBasic.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72C724E34D9100272C2D /* LexBasic.cxx */; };
		28BA733A24E34D9700272C2D /* LexCIL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72C824E34D9100272C2D /* LexCIL.cxx */; };
		28BA733B24E34D9700272C2D /* LexTCL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72C924E34D9100272C2D /* LexTCL.cxx */; };
		28BA733C24E34D9700272C2D /* LexMetapost.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72CA24E34D9100272C2D /* LexMetapost.cxx */; };
		28BA733D24E34D9700272C2D /* LexForth.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72CB24E34D9100272C2D /* LexForth.cxx */; };
		28BA733E24E34D9700272C2D /* LexSML.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72CC24E34D9100272C2D /* LexSML.cxx */; };
		28BA733F24E34D9700272C2D /* LexOScript.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72CD24E34D9100272C2D /* LexOScript.cxx */; };
		28BA734024E34D9700272C2D /* LexTACL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72CE24E34D9100272C2D /* LexTACL.cxx */; };
		28BA734124E34D9700272C2D /* LexGui4Cli.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72CF24E34D9100272C2D /* LexGui4Cli.cxx */; };
		28BA734224E34D9700272C2D /* LexCLW.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D024E34D9200272C2D /* LexCLW.cxx */; };
		28BA734324E34D9700272C2D /* LexRebol.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D124E34D9200272C2D /* LexRebol.cxx */; };
		28BA734424E34D9700272C2D /* LexSAS.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D224E34D9200272C2D /* LexSAS.cxx */; };
		28BA734524E34D9700272C2D /* LexNim.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D324E34D9200272C2D /* LexNim.cxx */; };
		28BA734624E34D9700272C2D /* LexSmalltalk.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D424E34D9200272C2D /* LexSmalltalk.cxx */; };
		28BA734724E34D9700272C2D /* LexModula.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D524E34D9200272C2D /* LexModula.cxx */; };
		28BA734824E34D9700272C2D /* LexBullant.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D624E34D9200272C2D /* LexBullant.cxx */; };
		28BA734924E34D9700272C2D /* LexASY.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D724E34D9200272C2D /* LexASY.cxx */; };
		28BA734A24E34D9700272C2D /* LexBash.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D824E34D9200272C2D /* LexBash.cxx */; };
		28BA734B24E34D9700272C2D /* LexEiffel.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72D924E34D9200272C2D /* LexEiffel.cxx */; };
		28BA734C24E34D9700272C2D /* LexVHDL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72DA24E34D9200272C2D /* LexVHDL.cxx */; };
		28BA734D24E34D9700272C2D /* LexAsn1.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72DB24E34D9200272C2D /* LexAsn1.cxx */; };
		28BA734E24E34D9700272C2D /* LexCoffeeScript.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72DC24E34D9200272C2D /* LexCoffeeScript.cxx */; };
		28BA734F24E34D9700272C2D /* LexDiff.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72DD24E34D9200272C2D /* LexDiff.cxx */; };
		28BA735024E34D9700272C2D /* LexSorcus.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72DE24E34D9200272C2D /* LexSorcus.cxx */; };
		28BA735124E34D9700272C2D /* LexAPDL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72DF24E34D9200272C2D /* LexAPDL.cxx */; };
		28BA735224E34D9700272C2D /* LexD.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E024E34D9200272C2D /* LexD.cxx */; };
		28BA735324E34D9700272C2D /* LexMySQL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E124E34D9200272C2D /* LexMySQL.cxx */; };
		28BA735424E34D9700272C2D /* LexHollywood.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E224E34D9200272C2D /* LexHollywood.cxx */; };
		28BA735524E34D9700272C2D /* LexProgress.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E324E34D9200272C2D /* LexProgress.cxx */; };
		28BA735624E34D9700272C2D /* LexLisp.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E424E34D9200272C2D /* LexLisp.cxx */; };
		28BA735724E34D9700272C2D /* LexPowerShell.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E524E34D9200272C2D /* LexPowerShell.cxx */; };
		28BA735824E34D9700272C2D /* LexPS.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E624E34D9200272C2D /* LexPS.cxx */; };
		28BA735924E34D9700272C2D /* LexYAML.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E724E34D9200272C2D /* LexYAML.cxx */; };
		28BA735A24E34D9700272C2D /* LexErlang.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E824E34D9200272C2D /* LexErlang.cxx */; };
		28BA735B24E34D9700272C2D /* LexRuby.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72E924E34D9300272C2D /* LexRuby.cxx */; };
		28BA735C24E34D9700272C2D /* LexIndent.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72EA24E34D9300272C2D /* LexIndent.cxx */; };
		28BA735D24E34D9700272C2D /* LexErrorList.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72EB24E34D9300272C2D /* LexErrorList.cxx */; };
		28BA735E24E34D9700272C2D /* LexFlagship.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72EC24E34D9300272C2D /* LexFlagship.cxx */; };
		28BA735F24E34D9700272C2D /* LexLaTeX.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72ED24E34D9300272C2D /* LexLaTeX.cxx */; };
		28BA736024E34D9700272C2D /* LexAbaqus.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72EE24E34D9300272C2D /* LexAbaqus.cxx */; };
		28BA736124E34D9700272C2D /* LexBatch.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72EF24E34D9300272C2D /* LexBatch.cxx */; };
		28BA736224E34D9700272C2D /* LexCPP.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F024E34D9300272C2D /* LexCPP.cxx */; };
		28BA736324E34D9700272C2D /* LexRaku.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F124E34D9300272C2D /* LexRaku.cxx */; };
		28BA736424E34D9700272C2D /* LexGAP.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F224E34D9300272C2D /* LexGAP.cxx */; };
		28BA736524E34D9700272C2D /* LexSQL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F324E34D9300272C2D /* LexSQL.cxx */; };
		28BA736624E34D9700272C2D /* LexNsis.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F424E34D9300272C2D /* LexNsis.cxx */; };
		28BA736724E34D9700272C2D /* LexEDIFACT.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F524E34D9300272C2D /* LexEDIFACT.cxx */; };
		28BA736824E34D9700272C2D /* LexEScript.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F624E34D9300272C2D /* LexEScript.cxx */; };
		28BA736924E34D9700272C2D /* LexPOV.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F724E34D9300272C2D /* LexPOV.cxx */; };
		28BA736A24E34D9700272C2D /* LexKVIrc.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F824E34D9300272C2D /* LexKVIrc.cxx */; };
		28BA736B24E34D9700272C2D /* LexSpecman.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72F924E34D9300272C2D /* LexSpecman.cxx */; };
		28BA736C24E34D9700272C2D /* LexHTML.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72FA24E34D9300272C2D /* LexHTML.cxx */; };
		28BA736D24E34D9700272C2D /* LexFortran.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72FB24E34D9400272C2D /* LexFortran.cxx */; };
		28BA736E24E34D9700272C2D /* LexRegistry.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72FC24E34D9400272C2D /* LexRegistry.cxx */; };
		28BA736F24E34D9700272C2D /* LexPython.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72FD24E34D9400272C2D /* LexPython.cxx */; };
		28BA737024E34D9700272C2D /* LexCmake.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72FE24E34D9400272C2D /* LexCmake.cxx */; };
		28BA737124E34D9700272C2D /* LexAsm.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA72FF24E34D9400272C2D /* LexAsm.cxx */; };
		28BA737224E34D9700272C2D /* LexAda.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730024E34D9400272C2D /* LexAda.cxx */; };
		28BA737324E34D9700272C2D /* LexCrontab.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730124E34D9400272C2D /* LexCrontab.cxx */; };
		28BA737424E34D9700272C2D /* LexDMIS.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730224E34D9400272C2D /* LexDMIS.cxx */; };
		28BA737524E34D9700272C2D /* LexTCMD.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730324E34D9400272C2D /* LexTCMD.cxx */; };
		28BA737624E34D9700272C2D /* LexConf.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730424E34D9400272C2D /* LexConf.cxx */; };
		28BA737724E34D9700272C2D /* LexInno.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730524E34D9400272C2D /* LexInno.cxx */; };
		28BA737824E34D9700272C2D /* LexA68k.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730624E34D9400272C2D /* LexA68k.cxx */; };
		28BA737924E34D9700272C2D /* LexMake.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730724E34D9400272C2D /* LexMake.cxx */; };
		28BA737A24E34D9700272C2D /* LexTeX.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730824E34D9400272C2D /* LexTeX.cxx */; };
		28BA737B24E34D9700272C2D /* LexSpice.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730924E34D9400272C2D /* LexSpice.cxx */; };
		28BA737C24E34D9700272C2D /* LexX12.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730A24E34D9400272C2D /* LexX12.cxx */; };
		28BA737D24E34D9700272C2D /* LexAU3.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730B24E34D9400272C2D /* LexAU3.cxx */; };
		28BA737E24E34D9700272C2D /* LexBaan.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730C24E34D9400272C2D /* LexBaan.cxx */; };
		28BA737F24E34D9700272C2D /* LexMPT.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730D24E34D9500272C2D /* LexMPT.cxx */; };
		28BA738024E34D9700272C2D /* LexTADS3.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730E24E34D9500272C2D /* LexTADS3.cxx */; };
		28BA738124E34D9700272C2D /* LexTxt2tags.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA730F24E34D9500272C2D /* LexTxt2tags.cxx */; };
		28BA738224E34D9700272C2D /* LexMMIXAL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731024E34D9500272C2D /* LexMMIXAL.cxx */; };
		28BA738324E34D9700272C2D /* LexKix.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731124E34D9500272C2D /* LexKix.cxx */; };
		28BA738424E34D9700272C2D /* LexSTTXT.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731224E34D9500272C2D /* LexSTTXT.cxx */; };
		28BA738524E34D9700272C2D /* LexMagik.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731324E34D9500272C2D /* LexMagik.cxx */; };
		28BA738624E34D9700272C2D /* LexNull.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731424E34D9500272C2D /* LexNull.cxx */; };
		28BA738724E34D9700272C2D /* LexCsound.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731524E34D9500272C2D /* LexCsound.cxx */; };
		28BA738824E34D9700272C2D /* LexLua.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731624E34D9500272C2D /* LexLua.cxx */; };
		28BA738924E34D9700272C2D /* LexStata.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731724E34D9500272C2D /* LexStata.cxx */; };
		28BA738A24E34D9700272C2D /* LexOpal.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731824E34D9500272C2D /* LexOpal.cxx */; };
		28BA738B24E34D9700272C2D /* LexHex.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731924E34D9500272C2D /* LexHex.cxx */; };
		28BA738C24E34D9700272C2D /* LexVerilog.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731A24E34D9500272C2D /* LexVerilog.cxx */; };
		28BA738D24E34D9700272C2D /* LexHaskell.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731B24E34D9500272C2D /* LexHaskell.cxx */; };
		28BA738E24E34D9700272C2D /* LexR.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731C24E34D9500272C2D /* LexR.cxx */; };
		28BA738F24E34D9700272C2D /* LexScriptol.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731D24E34D9500272C2D /* LexScriptol.cxx */; };
		28BA739024E34D9700272C2D /* LexVisualProlog.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731E24E34D9500272C2D /* LexVisualProlog.cxx */; };
		28BA739124E34D9700272C2D /* LexVB.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA731F24E34D9600272C2D /* LexVB.cxx */; };
		28BA739224E34D9700272C2D /* LexDMAP.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732024E34D9600272C2D /* LexDMAP.cxx */; };
		28BA739324E34D9700272C2D /* LexAVS.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732124E34D9600272C2D /* LexAVS.cxx */; };
		28BA739424E34D9700272C2D /* LexPB.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732224E34D9600272C2D /* LexPB.cxx */; };
		28BA739524E34D9700272C2D /* LexPO.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732324E34D9600272C2D /* LexPO.cxx */; };
		28BA739624E34D9700272C2D /* LexPowerPro.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732424E34D9600272C2D /* LexPowerPro.cxx */; };
		28BA739724E34D9700272C2D /* LexProps.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732524E34D9600272C2D /* LexProps.cxx */; };
		28BA739824E34D9700272C2D /* LexCOBOL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732624E34D9600272C2D /* LexCOBOL.cxx */; };
		28BA739924E34D9700272C2D /* LexPLM.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732724E34D9600272C2D /* LexPLM.cxx */; };
		28BA739A24E34D9700272C2D /* LexMSSQL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732824E34D9600272C2D /* LexMSSQL.cxx */; };
		28BA739B24E34D9700272C2D /* LexCSS.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732924E34D9600272C2D /* LexCSS.cxx */; };
		28BA739C24E34D9700272C2D /* LexMaxima.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732A24E34D9600272C2D /* LexMaxima.cxx */; };
		28BA739D24E34D9700272C2D /* LexCaml.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732B24E34D9600272C2D /* LexCaml.cxx */; };
		28BA739E24E34D9700272C2D /* LexDataflex.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732C24E34D9600272C2D /* LexDataflex.cxx */; };
		28BA739F24E34D9700272C2D /* LexLout.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732D24E34D9600272C2D /* LexLout.cxx */; };
		28BA73A024E34D9700272C2D /* LexTAL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732E24E34D9600272C2D /* LexTAL.cxx */; };
		28BA73A124E34D9700272C2D /* LexMarkdown.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA732F24E34D9600272C2D /* LexMarkdown.cxx */; };
		28BA73A224E34D9700272C2D /* LexJSON.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733024E34D9600272C2D /* LexJSON.cxx */; };
		28BA73A324E34D9700272C2D /* LexPascal.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733124E34D9700272C2D /* LexPascal.cxx */; };
		28BA73A424E34D9700272C2D /* LexAVE.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733224E34D9700272C2D /* LexAVE.cxx */; };
		28BA73A524E34D9700272C2D /* LexECL.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733324E34D9700272C2D /* LexECL.cxx */; };
		28BA73A624E34D9700272C2D /* LexMatlab.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733424E34D9700272C2D /* LexMatlab.cxx */; };
		28BA73A724E34D9700272C2D /* LexBibTeX.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733524E34D9700272C2D /* LexBibTeX.cxx */; };
		28BA73A824E34D9700272C2D /* LexNimrod.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733624E34D9700272C2D /* LexNimrod.cxx */; };
		28BA73A924E34D9700272C2D /* LexPerl.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733724E34D9700272C2D /* LexPerl.cxx */; };
		28BA73AA24E34D9700272C2D /* LexRust.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA733824E34D9700272C2D /* LexRust.cxx */; };
		28BA73AD24E34DBC00272C2D /* Lexilla.h in Headers */ = {isa = PBXBuildFile; fileRef = 28BA73AB24E34DBC00272C2D /* Lexilla.h */; };
		28BA73AE24E34DBC00272C2D /* Lexilla.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 28BA73AC24E34DBC00272C2D /* Lexilla.cxx */; };
		510D44AFB91EE873E86ABDD4 /* LexAsciidoc.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 3AF14420BFC43876F16C5995 /* LexAsciidoc.cxx */; };
		70BF497C8D265026B77C97DA /* LexJulia.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 315E4E969868C52C125686B2 /* LexJulia.cxx */; };
		B32D4A2A9CEC222A5140E99F /* LexFSharp.cxx in Sources */ = {isa = PBXBuildFile; fileRef = F8E54626B22BD9493090F40B /* LexFSharp.cxx */; };
		3D044C4CA34C6FD7E58E0091 /* LexTOML.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 42BC44CCB57D4E78DEE6E358 /* LexTOML.cxx */; };
		14314DCD945FDA90481F0A0D /* LexTroff.cxx in Sources */ = {isa = PBXBuildFile; fileRef = D2EF4913B8F91656C787F584 /* LexTroff.cxx */; };
		0DFB4F5F94B018794ADB389D /* LexDart.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 1F274010A7943C43BA265511 /* LexDart.cxx */; };
		CEC8496B8D9712E6EEDBC301 /* LexZig.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 71684CF6BCC80369BCE2F893 /* LexZig.cxx */; };
		4A444CF5A75E52E2C5537328 /* LexNix.cxx in Sources */ = {isa = PBXBuildFile; fileRef = 81E2488CB0A0DC6B67AA08DD /* LexNix.cxx */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		280262A5246DF655000DF3B8 /* liblexilla.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = liblexilla.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		283639BB268FD4EA009D58A1 /* LexAccessor.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAccessor.cxx; path = ../../lexlib/LexAccessor.cxx; sourceTree = "<group>"; };
		283A17AC2B47E61100DF5C82 /* InList.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = InList.cxx; path = ../../lexlib/InList.cxx; sourceTree = "<group>"; };
		283A17AD2B47E61100DF5C82 /* InList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = InList.h; path = ../../lexlib/InList.h; sourceTree = "<group>"; };
		28BA728F24E34D5A00272C2D /* LexerBase.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexerBase.cxx; path = ../../lexlib/LexerBase.cxx; sourceTree = "<group>"; };
		28BA729024E34D5A00272C2D /* LexAccessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = LexAccessor.h; path = ../../lexlib/LexAccessor.h; sourceTree = "<group>"; };
		28BA729124E34D5A00272C2D /* DefaultLexer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = DefaultLexer.h; path = ../../lexlib/DefaultLexer.h; sourceTree = "<group>"; };
		28BA729224E34D5A00272C2D /* SubStyles.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SubStyles.h; path = ../../lexlib/SubStyles.h; sourceTree = "<group>"; };
		28BA729424E34D5A00272C2D /* LexerModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = LexerModule.h; path = ../../lexlib/LexerModule.h; sourceTree = "<group>"; };
		28BA729524E34D5A00272C2D /* CharacterCategory.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = CharacterCategory.cxx; path = ../../lexlib/CharacterCategory.cxx; sourceTree = "<group>"; };
		28BA729624E34D5A00272C2D /* LexerSimple.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = LexerSimple.h; path = ../../lexlib/LexerSimple.h; sourceTree = "<group>"; };
		28BA729724E34D5A00272C2D /* Accessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Accessor.h; path = ../../lexlib/Accessor.h; sourceTree = "<group>"; };
		28BA729824E34D5A00272C2D /* PropSetSimple.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = PropSetSimple.cxx; path = ../../lexlib/PropSetSimple.cxx; sourceTree = "<group>"; };
		28BA729924E34D5A00272C2D /* CharacterSet.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = CharacterSet.cxx; path = ../../lexlib/CharacterSet.cxx; sourceTree = "<group>"; };
		28BA729A24E34D5A00272C2D /* SparseState.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SparseState.h; path = ../../lexlib/SparseState.h; sourceTree = "<group>"; };
		28BA729B24E34D5A00272C2D /* WordList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = WordList.h; path = ../../lexlib/WordList.h; sourceTree = "<group>"; };
		28BA729C24E34D5A00272C2D /* DefaultLexer.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = DefaultLexer.cxx; path = ../../lexlib/DefaultLexer.cxx; sourceTree = "<group>"; };
		28BA729E24E34D5A00272C2D /* WordList.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = WordList.cxx; path = ../../lexlib/WordList.cxx; sourceTree = "<group>"; };
		28BA729F24E34D5A00272C2D /* OptionSet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = OptionSet.h; path = ../../lexlib/OptionSet.h; sourceTree = "<group>"; };
		28BA72A024E34D5B00272C2D /* CatalogueModules.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CatalogueModules.h; path = ../../lexlib/CatalogueModules.h; sourceTree = "<group>"; };
		28BA72A124E34D5B00272C2D /* CharacterSet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CharacterSet.h; path = ../../lexlib/CharacterSet.h; sourceTree = "<group>"; };
		28BA72A224E34D5B00272C2D /* StyleContext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = StyleContext.h; path = ../../lexlib/StyleContext.h; sourceTree = "<group>"; };
		28BA72A324E34D5B00272C2D /* PropSetSimple.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PropSetSimple.h; path = ../../lexlib/PropSetSimple.h; sourceTree = "<group>"; };
		28BA72A424E34D5B00272C2D /* StringCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = StringCopy.h; path = ../../lexlib/StringCopy.h; sourceTree = "<group>"; };
		28BA72A524E34D5B00272C2D /* LexerModule.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexerModule.cxx; path = ../../lexlib/LexerModule.cxx; sourceTree = "<group>"; };
		28BA72A624E34D5B00272C2D /* LexerBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = LexerBase.h; path = ../../lexlib/LexerBase.h; sourceTree = "<group>"; };
		28BA72A724E34D5B00272C2D /* LexerSimple.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexerSimple.cxx; path = ../../lexlib/LexerSimple.cxx; sourceTree = "<group>"; };
		28BA72A824E34D5B00272C2D /* StyleContext.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = StyleContext.cxx; path = ../../lexlib/StyleContext.cxx; sourceTree = "<group>"; };
		28BA72A924E34D5B00272C2D /* CharacterCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = CharacterCategory.h; path = ../../lexlib/CharacterCategory.h; sourceTree = "<group>"; };
		28BA72AA24E34D5B00272C2D /* Accessor.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = Accessor.cxx; path = ../../lexlib/Accessor.cxx; sourceTree = "<group>"; };
		28BA72C724E34D9100272C2D /* LexBasic.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexBasic.cxx; path = ../../lexers/LexBasic.cxx; sourceTree = "<group>"; };
		28BA72C824E34D9100272C2D /* LexCIL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCIL.cxx; path = ../../lexers/LexCIL.cxx; sourceTree = "<group>"; };
		28BA72C924E34D9100272C2D /* LexTCL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTCL.cxx; path = ../../lexers/LexTCL.cxx; sourceTree = "<group>"; };
		28BA72CA24E34D9100272C2D /* LexMetapost.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMetapost.cxx; path = ../../lexers/LexMetapost.cxx; sourceTree = "<group>"; };
		28BA72CB24E34D9100272C2D /* LexForth.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexForth.cxx; path = ../../lexers/LexForth.cxx; sourceTree = "<group>"; };
		28BA72CC24E34D9100272C2D /* LexSML.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSML.cxx; path = ../../lexers/LexSML.cxx; sourceTree = "<group>"; };
		28BA72CD24E34D9100272C2D /* LexOScript.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexOScript.cxx; path = ../../lexers/LexOScript.cxx; sourceTree = "<group>"; };
		28BA72CE24E34D9100272C2D /* LexTACL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTACL.cxx; path = ../../lexers/LexTACL.cxx; sourceTree = "<group>"; };
		28BA72CF24E34D9100272C2D /* LexGui4Cli.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexGui4Cli.cxx; path = ../../lexers/LexGui4Cli.cxx; sourceTree = "<group>"; };
		28BA72D024E34D9200272C2D /* LexCLW.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCLW.cxx; path = ../../lexers/LexCLW.cxx; sourceTree = "<group>"; };
		28BA72D124E34D9200272C2D /* LexRebol.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexRebol.cxx; path = ../../lexers/LexRebol.cxx; sourceTree = "<group>"; };
		28BA72D224E34D9200272C2D /* LexSAS.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSAS.cxx; path = ../../lexers/LexSAS.cxx; sourceTree = "<group>"; };
		28BA72D324E34D9200272C2D /* LexNim.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexNim.cxx; path = ../../lexers/LexNim.cxx; sourceTree = "<group>"; };
		28BA72D424E34D9200272C2D /* LexSmalltalk.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSmalltalk.cxx; path = ../../lexers/LexSmalltalk.cxx; sourceTree = "<group>"; };
		28BA72D524E34D9200272C2D /* LexModula.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexModula.cxx; path = ../../lexers/LexModula.cxx; sourceTree = "<group>"; };
		28BA72D624E34D9200272C2D /* LexBullant.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexBullant.cxx; path = ../../lexers/LexBullant.cxx; sourceTree = "<group>"; };
		28BA72D724E34D9200272C2D /* LexASY.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexASY.cxx; path = ../../lexers/LexASY.cxx; sourceTree = "<group>"; };
		28BA72D824E34D9200272C2D /* LexBash.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexBash.cxx; path = ../../lexers/LexBash.cxx; sourceTree = "<group>"; };
		28BA72D924E34D9200272C2D /* LexEiffel.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexEiffel.cxx; path = ../../lexers/LexEiffel.cxx; sourceTree = "<group>"; };
		28BA72DA24E34D9200272C2D /* LexVHDL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexVHDL.cxx; path = ../../lexers/LexVHDL.cxx; sourceTree = "<group>"; };
		28BA72DB24E34D9200272C2D /* LexAsn1.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAsn1.cxx; path = ../../lexers/LexAsn1.cxx; sourceTree = "<group>"; };
		28BA72DC24E34D9200272C2D /* LexCoffeeScript.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCoffeeScript.cxx; path = ../../lexers/LexCoffeeScript.cxx; sourceTree = "<group>"; };
		28BA72DD24E34D9200272C2D /* LexDiff.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexDiff.cxx; path = ../../lexers/LexDiff.cxx; sourceTree = "<group>"; };
		28BA72DE24E34D9200272C2D /* LexSorcus.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSorcus.cxx; path = ../../lexers/LexSorcus.cxx; sourceTree = "<group>"; };
		28BA72DF24E34D9200272C2D /* LexAPDL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAPDL.cxx; path = ../../lexers/LexAPDL.cxx; sourceTree = "<group>"; };
		28BA72E024E34D9200272C2D /* LexD.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexD.cxx; path = ../../lexers/LexD.cxx; sourceTree = "<group>"; };
		28BA72E124E34D9200272C2D /* LexMySQL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMySQL.cxx; path = ../../lexers/LexMySQL.cxx; sourceTree = "<group>"; };
		28BA72E224E34D9200272C2D /* LexHollywood.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexHollywood.cxx; path = ../../lexers/LexHollywood.cxx; sourceTree = "<group>"; };
		28BA72E324E34D9200272C2D /* LexProgress.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexProgress.cxx; path = ../../lexers/LexProgress.cxx; sourceTree = "<group>"; };
		28BA72E424E34D9200272C2D /* LexLisp.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexLisp.cxx; path = ../../lexers/LexLisp.cxx; sourceTree = "<group>"; };
		28BA72E524E34D9200272C2D /* LexPowerShell.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPowerShell.cxx; path = ../../lexers/LexPowerShell.cxx; sourceTree = "<group>"; };
		28BA72E624E34D9200272C2D /* LexPS.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPS.cxx; path = ../../lexers/LexPS.cxx; sourceTree = "<group>"; };
		28BA72E724E34D9200272C2D /* LexYAML.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexYAML.cxx; path = ../../lexers/LexYAML.cxx; sourceTree = "<group>"; };
		28BA72E824E34D9200272C2D /* LexErlang.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexErlang.cxx; path = ../../lexers/LexErlang.cxx; sourceTree = "<group>"; };
		28BA72E924E34D9300272C2D /* LexRuby.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexRuby.cxx; path = ../../lexers/LexRuby.cxx; sourceTree = "<group>"; };
		28BA72EA24E34D9300272C2D /* LexIndent.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexIndent.cxx; path = ../../lexers/LexIndent.cxx; sourceTree = "<group>"; };
		28BA72EB24E34D9300272C2D /* LexErrorList.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexErrorList.cxx; path = ../../lexers/LexErrorList.cxx; sourceTree = "<group>"; };
		28BA72EC24E34D9300272C2D /* LexFlagship.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexFlagship.cxx; path = ../../lexers/LexFlagship.cxx; sourceTree = "<group>"; };
		28BA72ED24E34D9300272C2D /* LexLaTeX.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexLaTeX.cxx; path = ../../lexers/LexLaTeX.cxx; sourceTree = "<group>"; };
		28BA72EE24E34D9300272C2D /* LexAbaqus.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAbaqus.cxx; path = ../../lexers/LexAbaqus.cxx; sourceTree = "<group>"; };
		28BA72EF24E34D9300272C2D /* LexBatch.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexBatch.cxx; path = ../../lexers/LexBatch.cxx; sourceTree = "<group>"; };
		28BA72F024E34D9300272C2D /* LexCPP.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCPP.cxx; path = ../../lexers/LexCPP.cxx; sourceTree = "<group>"; };
		28BA72F124E34D9300272C2D /* LexRaku.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexRaku.cxx; path = ../../lexers/LexRaku.cxx; sourceTree = "<group>"; };
		28BA72F224E34D9300272C2D /* LexGAP.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexGAP.cxx; path = ../../lexers/LexGAP.cxx; sourceTree = "<group>"; };
		28BA72F324E34D9300272C2D /* LexSQL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSQL.cxx; path = ../../lexers/LexSQL.cxx; sourceTree = "<group>"; };
		28BA72F424E34D9300272C2D /* LexNsis.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexNsis.cxx; path = ../../lexers/LexNsis.cxx; sourceTree = "<group>"; };
		28BA72F524E34D9300272C2D /* LexEDIFACT.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexEDIFACT.cxx; path = ../../lexers/LexEDIFACT.cxx; sourceTree = "<group>"; };
		28BA72F624E34D9300272C2D /* LexEScript.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexEScript.cxx; path = ../../lexers/LexEScript.cxx; sourceTree = "<group>"; };
		28BA72F724E34D9300272C2D /* LexPOV.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPOV.cxx; path = ../../lexers/LexPOV.cxx; sourceTree = "<group>"; };
		28BA72F824E34D9300272C2D /* LexKVIrc.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexKVIrc.cxx; path = ../../lexers/LexKVIrc.cxx; sourceTree = "<group>"; };
		28BA72F924E34D9300272C2D /* LexSpecman.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSpecman.cxx; path = ../../lexers/LexSpecman.cxx; sourceTree = "<group>"; };
		28BA72FA24E34D9300272C2D /* LexHTML.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexHTML.cxx; path = ../../lexers/LexHTML.cxx; sourceTree = "<group>"; };
		28BA72FB24E34D9400272C2D /* LexFortran.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexFortran.cxx; path = ../../lexers/LexFortran.cxx; sourceTree = "<group>"; };
		28BA72FC24E34D9400272C2D /* LexRegistry.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexRegistry.cxx; path = ../../lexers/LexRegistry.cxx; sourceTree = "<group>"; };
		28BA72FD24E34D9400272C2D /* LexPython.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPython.cxx; path = ../../lexers/LexPython.cxx; sourceTree = "<group>"; };
		28BA72FE24E34D9400272C2D /* LexCmake.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCmake.cxx; path = ../../lexers/LexCmake.cxx; sourceTree = "<group>"; };
		28BA72FF24E34D9400272C2D /* LexAsm.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAsm.cxx; path = ../../lexers/LexAsm.cxx; sourceTree = "<group>"; };
		28BA730024E34D9400272C2D /* LexAda.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAda.cxx; path = ../../lexers/LexAda.cxx; sourceTree = "<group>"; };
		28BA730124E34D9400272C2D /* LexCrontab.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCrontab.cxx; path = ../../lexers/LexCrontab.cxx; sourceTree = "<group>"; };
		28BA730224E34D9400272C2D /* LexDMIS.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexDMIS.cxx; path = ../../lexers/LexDMIS.cxx; sourceTree = "<group>"; };
		28BA730324E34D9400272C2D /* LexTCMD.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTCMD.cxx; path = ../../lexers/LexTCMD.cxx; sourceTree = "<group>"; };
		28BA730424E34D9400272C2D /* LexConf.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexConf.cxx; path = ../../lexers/LexConf.cxx; sourceTree = "<group>"; };
		28BA730524E34D9400272C2D /* LexInno.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexInno.cxx; path = ../../lexers/LexInno.cxx; sourceTree = "<group>"; };
		28BA730624E34D9400272C2D /* LexA68k.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexA68k.cxx; path = ../../lexers/LexA68k.cxx; sourceTree = "<group>"; };
		28BA730724E34D9400272C2D /* LexMake.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMake.cxx; path = ../../lexers/LexMake.cxx; sourceTree = "<group>"; };
		28BA730824E34D9400272C2D /* LexTeX.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTeX.cxx; path = ../../lexers/LexTeX.cxx; sourceTree = "<group>"; };
		28BA730924E34D9400272C2D /* LexSpice.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSpice.cxx; path = ../../lexers/LexSpice.cxx; sourceTree = "<group>"; };
		28BA730A24E34D9400272C2D /* LexX12.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexX12.cxx; path = ../../lexers/LexX12.cxx; sourceTree = "<group>"; };
		28BA730B24E34D9400272C2D /* LexAU3.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAU3.cxx; path = ../../lexers/LexAU3.cxx; sourceTree = "<group>"; };
		28BA730C24E34D9400272C2D /* LexBaan.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexBaan.cxx; path = ../../lexers/LexBaan.cxx; sourceTree = "<group>"; };
		28BA730D24E34D9500272C2D /* LexMPT.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMPT.cxx; path = ../../lexers/LexMPT.cxx; sourceTree = "<group>"; };
		28BA730E24E34D9500272C2D /* LexTADS3.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTADS3.cxx; path = ../../lexers/LexTADS3.cxx; sourceTree = "<group>"; };
		28BA730F24E34D9500272C2D /* LexTxt2tags.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTxt2tags.cxx; path = ../../lexers/LexTxt2tags.cxx; sourceTree = "<group>"; };
		28BA731024E34D9500272C2D /* LexMMIXAL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMMIXAL.cxx; path = ../../lexers/LexMMIXAL.cxx; sourceTree = "<group>"; };
		28BA731124E34D9500272C2D /* LexKix.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexKix.cxx; path = ../../lexers/LexKix.cxx; sourceTree = "<group>"; };
		28BA731224E34D9500272C2D /* LexSTTXT.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexSTTXT.cxx; path = ../../lexers/LexSTTXT.cxx; sourceTree = "<group>"; };
		28BA731324E34D9500272C2D /* LexMagik.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMagik.cxx; path = ../../lexers/LexMagik.cxx; sourceTree = "<group>"; };
		28BA731424E34D9500272C2D /* LexNull.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexNull.cxx; path = ../../lexers/LexNull.cxx; sourceTree = "<group>"; };
		28BA731524E34D9500272C2D /* LexCsound.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCsound.cxx; path = ../../lexers/LexCsound.cxx; sourceTree = "<group>"; };
		28BA731624E34D9500272C2D /* LexLua.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexLua.cxx; path = ../../lexers/LexLua.cxx; sourceTree = "<group>"; };
		28BA731724E34D9500272C2D /* LexStata.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexStata.cxx; path = ../../lexers/LexStata.cxx; sourceTree = "<group>"; };
		28BA731824E34D9500272C2D /* LexOpal.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexOpal.cxx; path = ../../lexers/LexOpal.cxx; sourceTree = "<group>"; };
		28BA731924E34D9500272C2D /* LexHex.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexHex.cxx; path = ../../lexers/LexHex.cxx; sourceTree = "<group>"; };
		28BA731A24E34D9500272C2D /* LexVerilog.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexVerilog.cxx; path = ../../lexers/LexVerilog.cxx; sourceTree = "<group>"; };
		28BA731B24E34D9500272C2D /* LexHaskell.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexHaskell.cxx; path = ../../lexers/LexHaskell.cxx; sourceTree = "<group>"; };
		28BA731C24E34D9500272C2D /* LexR.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexR.cxx; path = ../../lexers/LexR.cxx; sourceTree = "<group>"; };
		28BA731D24E34D9500272C2D /* LexScriptol.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexScriptol.cxx; path = ../../lexers/LexScriptol.cxx; sourceTree = "<group>"; };
		28BA731E24E34D9500272C2D /* LexVisualProlog.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexVisualProlog.cxx; path = ../../lexers/LexVisualProlog.cxx; sourceTree = "<group>"; };
		28BA731F24E34D9600272C2D /* LexVB.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexVB.cxx; path = ../../lexers/LexVB.cxx; sourceTree = "<group>"; };
		28BA732024E34D9600272C2D /* LexDMAP.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexDMAP.cxx; path = ../../lexers/LexDMAP.cxx; sourceTree = "<group>"; };
		28BA732124E34D9600272C2D /* LexAVS.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAVS.cxx; path = ../../lexers/LexAVS.cxx; sourceTree = "<group>"; };
		28BA732224E34D9600272C2D /* LexPB.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPB.cxx; path = ../../lexers/LexPB.cxx; sourceTree = "<group>"; };
		28BA732324E34D9600272C2D /* LexPO.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPO.cxx; path = ../../lexers/LexPO.cxx; sourceTree = "<group>"; };
		28BA732424E34D9600272C2D /* LexPowerPro.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPowerPro.cxx; path = ../../lexers/LexPowerPro.cxx; sourceTree = "<group>"; };
		28BA732524E34D9600272C2D /* LexProps.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexProps.cxx; path = ../../lexers/LexProps.cxx; sourceTree = "<group>"; };
		28BA732624E34D9600272C2D /* LexCOBOL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCOBOL.cxx; path = ../../lexers/LexCOBOL.cxx; sourceTree = "<group>"; };
		28BA732724E34D9600272C2D /* LexPLM.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPLM.cxx; path = ../../lexers/LexPLM.cxx; sourceTree = "<group>"; };
		28BA732824E34D9600272C2D /* LexMSSQL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMSSQL.cxx; path = ../../lexers/LexMSSQL.cxx; sourceTree = "<group>"; };
		28BA732924E34D9600272C2D /* LexCSS.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCSS.cxx; path = ../../lexers/LexCSS.cxx; sourceTree = "<group>"; };
		28BA732A24E34D9600272C2D /* LexMaxima.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMaxima.cxx; path = ../../lexers/LexMaxima.cxx; sourceTree = "<group>"; };
		28BA732B24E34D9600272C2D /* LexCaml.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexCaml.cxx; path = ../../lexers/LexCaml.cxx; sourceTree = "<group>"; };
		28BA732C24E34D9600272C2D /* LexDataflex.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexDataflex.cxx; path = ../../lexers/LexDataflex.cxx; sourceTree = "<group>"; };
		28BA732D24E34D9600272C2D /* LexLout.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexLout.cxx; path = ../../lexers/LexLout.cxx; sourceTree = "<group>"; };
		28BA732E24E34D9600272C2D /* LexTAL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTAL.cxx; path = ../../lexers/LexTAL.cxx; sourceTree = "<group>"; };
		28BA732F24E34D9600272C2D /* LexMarkdown.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMarkdown.cxx; path = ../../lexers/LexMarkdown.cxx; sourceTree = "<group>"; };
		28BA733024E34D9600272C2D /* LexJSON.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexJSON.cxx; path = ../../lexers/LexJSON.cxx; sourceTree = "<group>"; };
		28BA733124E34D9700272C2D /* LexPascal.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPascal.cxx; path = ../../lexers/LexPascal.cxx; sourceTree = "<group>"; };
		28BA733224E34D9700272C2D /* LexAVE.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAVE.cxx; path = ../../lexers/LexAVE.cxx; sourceTree = "<group>"; };
		28BA733324E34D9700272C2D /* LexECL.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexECL.cxx; path = ../../lexers/LexECL.cxx; sourceTree = "<group>"; };
		28BA733424E34D9700272C2D /* LexMatlab.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexMatlab.cxx; path = ../../lexers/LexMatlab.cxx; sourceTree = "<group>"; };
		28BA733524E34D9700272C2D /* LexBibTeX.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexBibTeX.cxx; path = ../../lexers/LexBibTeX.cxx; sourceTree = "<group>"; };
		28BA733624E34D9700272C2D /* LexNimrod.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexNimrod.cxx; path = ../../lexers/LexNimrod.cxx; sourceTree = "<group>"; };
		28BA733724E34D9700272C2D /* LexPerl.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexPerl.cxx; path = ../../lexers/LexPerl.cxx; sourceTree = "<group>"; };
		28BA733824E34D9700272C2D /* LexRust.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexRust.cxx; path = ../../lexers/LexRust.cxx; sourceTree = "<group>"; };
		28BA73AB24E34DBC00272C2D /* Lexilla.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Lexilla.h; path = ../../include/Lexilla.h; sourceTree = "<group>"; };
		28BA73AC24E34DBC00272C2D /* Lexilla.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = Lexilla.cxx; path = ../Lexilla.cxx; sourceTree = "<group>"; };
		28BA73B024E3510900272C2D /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		315E4E969868C52C125686B2 /* LexJulia.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexJulia.cxx; path = ../../lexers/LexJulia.cxx; sourceTree = SOURCE_ROOT; };
		3AF14420BFC43876F16C5995 /* LexAsciidoc.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexAsciidoc.cxx; path = ../../lexers/LexAsciidoc.cxx; sourceTree = SOURCE_ROOT; };
		A383409E9A994F461550FEC1 /* LexGDScript.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexGDScript.cxx; path = ../../lexers/LexGDScript.cxx; sourceTree = SOURCE_ROOT; };
		F8E54626B22BD9493090F40B /* LexFSharp.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexFSharp.cxx; path = ../../lexers/LexFSharp.cxx; sourceTree = SOURCE_ROOT; };
		42BC44CCB57D4E78DEE6E358 /* LexTOML.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTOML.cxx; path = ../../lexers/LexTOML.cxx; sourceTree = SOURCE_ROOT; };
		D2EF4913B8F91656C787F584 /* LexTroff.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexTroff.cxx; path = ../../lexers/LexTroff.cxx; sourceTree = SOURCE_ROOT; };
		1F274010A7943C43BA265511 /* LexDart.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexDart.cxx; path = ../../lexers/LexDart.cxx; sourceTree = SOURCE_ROOT; };
		71684CF6BCC80369BCE2F893 /* LexZig.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexZig.cxx; path = ../../lexers/LexZig.cxx; sourceTree = SOURCE_ROOT; };
		81E2488CB0A0DC6B67AA08DD /* LexNix.cxx */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = LexNix.cxx; path = ../../lexers/LexNix.cxx; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		280262A3246DF655000DF3B8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2802629C246DF655000DF3B8 = {
			isa = PBXGroup;
			children = (
				28BA73B024E3510900272C2D /* Info.plist */,
				280262B8246DF776000DF3B8 /* LexLib */,
				280262B7246DF765000DF3B8 /* Lexers */,
				280262A7246DF655000DF3B8 /* Lexilla */,
				280262A6246DF655000DF3B8 /* Products */,
			);
			sourceTree = "<group>";
		};
		280262A6246DF655000DF3B8 /* Products */ = {
			isa = PBXGroup;
			children = (
				280262A5246DF655000DF3B8 /* liblexilla.dylib */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		280262A7246DF655000DF3B8 /* Lexilla */ = {
			isa = PBXGroup;
			children = (
				28BA73AC24E34DBC00272C2D /* Lexilla.cxx */,
				28BA73AB24E34DBC00272C2D /* Lexilla.h */,
			);
			name = Lexilla;
			sourceTree = "<group>";
		};
		280262B7246DF765000DF3B8 /* Lexers */ = {
			isa = PBXGroup;
			children = (
				28BA730624E34D9400272C2D /* LexA68k.cxx */,
				28BA72EE24E34D9300272C2D /* LexAbaqus.cxx */,
				28BA730024E34D9400272C2D /* LexAda.cxx */,
				28BA72DF24E34D9200272C2D /* LexAPDL.cxx */,
				3AF14420BFC43876F16C5995 /* LexAsciidoc.cxx */,
				28BA72FF24E34D9400272C2D /* LexAsm.cxx */,
				28BA72DB24E34D9200272C2D /* LexAsn1.cxx */,
				28BA72D724E34D9200272C2D /* LexASY.cxx */,
				28BA730B24E34D9400272C2D /* LexAU3.cxx */,
				28BA733224E34D9700272C2D /* LexAVE.cxx */,
				28BA732124E34D9600272C2D /* LexAVS.cxx */,
				28BA730C24E34D9400272C2D /* LexBaan.cxx */,
				28BA72D824E34D9200272C2D /* LexBash.cxx */,
				28BA72C724E34D9100272C2D /* LexBasic.cxx */,
				28BA72EF24E34D9300272C2D /* LexBatch.cxx */,
				28BA733524E34D9700272C2D /* LexBibTeX.cxx */,
				28BA72D624E34D9200272C2D /* LexBullant.cxx */,
				28BA732B24E34D9600272C2D /* LexCaml.cxx */,
				28BA72C824E34D9100272C2D /* LexCIL.cxx */,
				28BA72D024E34D9200272C2D /* LexCLW.cxx */,
				28BA72FE24E34D9400272C2D /* LexCmake.cxx */,
				28BA732624E34D9600272C2D /* LexCOBOL.cxx */,
				28BA72DC24E34D9200272C2D /* LexCoffeeScript.cxx */,
				28BA730424E34D9400272C2D /* LexConf.cxx */,
				28BA72F024E34D9300272C2D /* LexCPP.cxx */,
				28BA730124E34D9400272C2D /* LexCrontab.cxx */,
				28BA731524E34D9500272C2D /* LexCsound.cxx */,
				28BA732924E34D9600272C2D /* LexCSS.cxx */,
				28BA72E024E34D9200272C2D /* LexD.cxx */,
				1F274010A7943C43BA265511 /* LexDart.cxx */,
				28BA732C24E34D9600272C2D /* LexDataflex.cxx */,
				28BA72DD24E34D9200272C2D /* LexDiff.cxx */,
				28BA732024E34D9600272C2D /* LexDMAP.cxx */,
				28BA730224E34D9400272C2D /* LexDMIS.cxx */,
				28BA733324E34D9700272C2D /* LexECL.cxx */,
				28BA72F524E34D9300272C2D /* LexEDIFACT.cxx */,
				28BA72D924E34D9200272C2D /* LexEiffel.cxx */,
				28BA72E824E34D9200272C2D /* LexErlang.cxx */,
				28BA72EB24E34D9300272C2D /* LexErrorList.cxx */,
				28BA72F624E34D9300272C2D /* LexEScript.cxx */,
				28BA72EC24E34D9300272C2D /* LexFlagship.cxx */,
				28BA72CB24E34D9100272C2D /* LexForth.cxx */,
				28BA72FB24E34D9400272C2D /* LexFortran.cxx */,
				F8E54626B22BD9493090F40B /* LexFSharp.cxx */,
				28BA72F224E34D9300272C2D /* LexGAP.cxx */,
				A383409E9A994F461550FEC1 /* LexGDScript.cxx */,
				28BA72CF24E34D9100272C2D /* LexGui4Cli.cxx */,
				28BA731B24E34D9500272C2D /* LexHaskell.cxx */,
				28BA731924E34D9500272C2D /* LexHex.cxx */,
				28BA72E224E34D9200272C2D /* LexHollywood.cxx */,
				28BA72FA24E34D9300272C2D /* LexHTML.cxx */,
				28BA72EA24E34D9300272C2D /* LexIndent.cxx */,
				28BA730524E34D9400272C2D /* LexInno.cxx */,
				28BA733024E34D9600272C2D /* LexJSON.cxx */,
				315E4E969868C52C125686B2 /* LexJulia.cxx */,
				28BA731124E34D9500272C2D /* LexKix.cxx */,
				28BA72F824E34D9300272C2D /* LexKVIrc.cxx */,
				28BA72ED24E34D9300272C2D /* LexLaTeX.cxx */,
				28BA72E424E34D9200272C2D /* LexLisp.cxx */,
				28BA732D24E34D9600272C2D /* LexLout.cxx */,
				28BA731624E34D9500272C2D /* LexLua.cxx */,
				28BA731324E34D9500272C2D /* LexMagik.cxx */,
				28BA730724E34D9400272C2D /* LexMake.cxx */,
				28BA732F24E34D9600272C2D /* LexMarkdown.cxx */,
				28BA733424E34D9700272C2D /* LexMatlab.cxx */,
				28BA732A24E34D9600272C2D /* LexMaxima.cxx */,
				28BA72CA24E34D9100272C2D /* LexMetapost.cxx */,
				28BA731024E34D9500272C2D /* LexMMIXAL.cxx */,
				28BA72D524E34D9200272C2D /* LexModula.cxx */,
				28BA730D24E34D9500272C2D /* LexMPT.cxx */,
				28BA732824E34D9600272C2D /* LexMSSQL.cxx */,
				28BA72E124E34D9200272C2D /* LexMySQL.cxx */,
				28BA72D324E34D9200272C2D /* LexNim.cxx */,
				28BA733624E34D9700272C2D /* LexNimrod.cxx */,
				81E2488CB0A0DC6B67AA08DD /* LexNix.cxx */,
				28BA72F424E34D9300272C2D /* LexNsis.cxx */,
				28BA731424E34D9500272C2D /* LexNull.cxx */,
				28BA731824E34D9500272C2D /* LexOpal.cxx */,
				28BA72CD24E34D9100272C2D /* LexOScript.cxx */,
				28BA733124E34D9700272C2D /* LexPascal.cxx */,
				28BA732224E34D9600272C2D /* LexPB.cxx */,
				28BA733724E34D9700272C2D /* LexPerl.cxx */,
				28BA732724E34D9600272C2D /* LexPLM.cxx */,
				28BA732324E34D9600272C2D /* LexPO.cxx */,
				28BA72F724E34D9300272C2D /* LexPOV.cxx */,
				28BA732424E34D9600272C2D /* LexPowerPro.cxx */,
				28BA72E524E34D9200272C2D /* LexPowerShell.cxx */,
				28BA72E324E34D9200272C2D /* LexProgress.cxx */,
				28BA732524E34D9600272C2D /* LexProps.cxx */,
				28BA72E624E34D9200272C2D /* LexPS.cxx */,
				28BA72FD24E34D9400272C2D /* LexPython.cxx */,
				28BA731C24E34D9500272C2D /* LexR.cxx */,
				28BA72F124E34D9300272C2D /* LexRaku.cxx */,
				28BA72D124E34D9200272C2D /* LexRebol.cxx */,
				28BA72FC24E34D9400272C2D /* LexRegistry.cxx */,
				28BA72E924E34D9300272C2D /* LexRuby.cxx */,
				28BA733824E34D9700272C2D /* LexRust.cxx */,
				28BA72D224E34D9200272C2D /* LexSAS.cxx */,
				28BA731D24E34D9500272C2D /* LexScriptol.cxx */,
				28BA72D424E34D9200272C2D /* LexSmalltalk.cxx */,
				28BA72CC24E34D9100272C2D /* LexSML.cxx */,
				28BA72DE24E34D9200272C2D /* LexSorcus.cxx */,
				28BA72F924E34D9300272C2D /* LexSpecman.cxx */,
				28BA730924E34D9400272C2D /* LexSpice.cxx */,
				28BA72F324E34D9300272C2D /* LexSQL.cxx */,
				28BA731724E34D9500272C2D /* LexStata.cxx */,
				28BA731224E34D9500272C2D /* LexSTTXT.cxx */,
				28BA72CE24E34D9100272C2D /* LexTACL.cxx */,
				28BA730E24E34D9500272C2D /* LexTADS3.cxx */,
				28BA732E24E34D9600272C2D /* LexTAL.cxx */,
				28BA72C924E34D9100272C2D /* LexTCL.cxx */,
				28BA730324E34D9400272C2D /* LexTCMD.cxx */,
				28BA730824E34D9400272C2D /* LexTeX.cxx */,
				42BC44CCB57D4E78DEE6E358 /* LexTOML.cxx */,
				D2EF4913B8F91656C787F584 /* LexTroff.cxx */,
				28BA730F24E34D9500272C2D /* LexTxt2tags.cxx */,
				28BA731F24E34D9600272C2D /* LexVB.cxx */,
				28BA731A24E34D9500272C2D /* LexVerilog.cxx */,
				28BA72DA24E34D9200272C2D /* LexVHDL.cxx */,
				28BA731E24E34D9500272C2D /* LexVisualProlog.cxx */,
				28BA730A24E34D9400272C2D /* LexX12.cxx */,
				28BA72E724E34D9200272C2D /* LexYAML.cxx */,
				71684CF6BCC80369BCE2F893 /* LexZig.cxx */,
			);
			name = Lexers;
			sourceTree = "<group>";
		};
		280262B8246DF776000DF3B8 /* LexLib */ = {
			isa = PBXGroup;
			children = (
				28BA72AA24E34D5B00272C2D /* Accessor.cxx */,
				28BA729724E34D5A00272C2D /* Accessor.h */,
				28BA72A024E34D5B00272C2D /* CatalogueModules.h */,
				28BA729524E34D5A00272C2D /* CharacterCategory.cxx */,
				28BA72A924E34D5B00272C2D /* CharacterCategory.h */,
				28BA729924E34D5A00272C2D /* CharacterSet.cxx */,
				28BA72A124E34D5B00272C2D /* CharacterSet.h */,
				28BA729C24E34D5A00272C2D /* DefaultLexer.cxx */,
				28BA729124E34D5A00272C2D /* DefaultLexer.h */,
				283A17AC2B47E61100DF5C82 /* InList.cxx */,
				283A17AD2B47E61100DF5C82 /* InList.h */,
				283639BB268FD4EA009D58A1 /* LexAccessor.cxx */,
				28BA729024E34D5A00272C2D /* LexAccessor.h */,
				28BA728F24E34D5A00272C2D /* LexerBase.cxx */,
				28BA72A624E34D5B00272C2D /* LexerBase.h */,
				28BA72A524E34D5B00272C2D /* LexerModule.cxx */,
				28BA729424E34D5A00272C2D /* LexerModule.h */,
				28BA72A724E34D5B00272C2D /* LexerSimple.cxx */,
				28BA729624E34D5A00272C2D /* LexerSimple.h */,
				28BA729F24E34D5A00272C2D /* OptionSet.h */,
				28BA729824E34D5A00272C2D /* PropSetSimple.cxx */,
				28BA72A324E34D5B00272C2D /* PropSetSimple.h */,
				28BA729A24E34D5A00272C2D /* SparseState.h */,
				28BA72A424E34D5B00272C2D /* StringCopy.h */,
				28BA72A824E34D5B00272C2D /* StyleContext.cxx */,
				28BA72A224E34D5B00272C2D /* StyleContext.h */,
				28BA729224E34D5A00272C2D /* SubStyles.h */,
				28BA729E24E34D5A00272C2D /* WordList.cxx */,
				28BA729B24E34D5A00272C2D /* WordList.h */,
			);
			name = LexLib;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		280262A1246DF655000DF3B8 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28BA73AD24E34DBC00272C2D /* Lexilla.h in Headers */,
				28BA72BF24E34D5B00272C2D /* PropSetSimple.h in Headers */,
				28BA72B224E34D5B00272C2D /* LexerSimple.h in Headers */,
				28BA72B724E34D5B00272C2D /* WordList.h in Headers */,
				28BA72C024E34D5B00272C2D /* StringCopy.h in Headers */,
				28BA72AD24E34D5B00272C2D /* DefaultLexer.h in Headers */,
				28BA72B324E34D5B00272C2D /* Accessor.h in Headers */,
				28BA72BE24E34D5B00272C2D /* StyleContext.h in Headers */,
				28BA72BB24E34D5B00272C2D /* OptionSet.h in Headers */,
				283A17AF2B47E61100DF5C82 /* InList.h in Headers */,
				28BA72B024E34D5B00272C2D /* LexerModule.h in Headers */,
				28BA72AC24E34D5B00272C2D /* LexAccessor.h in Headers */,
				28BA72C524E34D5B00272C2D /* CharacterCategory.h in Headers */,
				28BA72BD24E34D5B00272C2D /* CharacterSet.h in Headers */,
				28BA72AE24E34D5B00272C2D /* SubStyles.h in Headers */,
				28BA72BC24E34D5B00272C2D /* CatalogueModules.h in Headers */,
				28BA72C224E34D5B00272C2D /* LexerBase.h in Headers */,
				28BA72B624E34D5B00272C2D /* SparseState.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		280262A4246DF655000DF3B8 /* lexilla */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 280262B0246DF655000DF3B8 /* Build configuration list for PBXNativeTarget "lexilla" */;
			buildPhases = (
				280262A1246DF655000DF3B8 /* Headers */,
				280262A2246DF655000DF3B8 /* Sources */,
				280262A3246DF655000DF3B8 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = lexilla;
			productName = lexilla;
			productReference = 280262A5246DF655000DF3B8 /* liblexilla.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2802629D246DF655000DF3B8 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1500;
				ORGANIZATIONNAME = "Neil Hodgson";
				TargetAttributes = {
					280262A4246DF655000DF3B8 = {
						CreatedOnToolsVersion = 11.4.1;
					};
				};
			};
			buildConfigurationList = 280262A0246DF655000DF3B8 /* Build configuration list for PBXProject "Lexilla" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2802629C246DF655000DF3B8;
			productRefGroup = 280262A6246DF655000DF3B8 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				280262A4246DF655000DF3B8 /* lexilla */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		280262A2246DF655000DF3B8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28BA739A24E34D9700272C2D /* LexMSSQL.cxx in Sources */,
				28BA735324E34D9700272C2D /* LexMySQL.cxx in Sources */,
				28BA738024E34D9700272C2D /* LexTADS3.cxx in Sources */,
				28BA73A924E34D9700272C2D /* LexPerl.cxx in Sources */,
				28BA733D24E34D9700272C2D /* LexForth.cxx in Sources */,
				28BA736824E34D9700272C2D /* LexEScript.cxx in Sources */,
				283639BC268FD4EA009D58A1 /* LexAccessor.cxx in Sources */,
				28BA737124E34D9700272C2D /* LexAsm.cxx in Sources */,
				28BA737B24E34D9700272C2D /* LexSpice.cxx in Sources */,
				28BA737024E34D9700272C2D /* LexCmake.cxx in Sources */,
				28BA734624E34D9700272C2D /* LexSmalltalk.cxx in Sources */,
				28BA72C424E34D5B00272C2D /* StyleContext.cxx in Sources */,
				28BA734C24E34D9700272C2D /* LexVHDL.cxx in Sources */,
				28BA737724E34D9700272C2D /* LexInno.cxx in Sources */,
				28BA739B24E34D9700272C2D /* LexCSS.cxx in Sources */,
				28BA734A24E34D9700272C2D /* LexBash.cxx in Sources */,
				28BA734224E34D9700272C2D /* LexCLW.cxx in Sources */,
				28BA734424E34D9700272C2D /* LexSAS.cxx in Sources */,
				28BA738E24E34D9700272C2D /* LexR.cxx in Sources */,
				28BA72C124E34D5B00272C2D /* LexerModule.cxx in Sources */,
				28BA735C24E34D9700272C2D /* LexIndent.cxx in Sources */,
				28BA736624E34D9700272C2D /* LexNsis.cxx in Sources */,
				28BA734724E34D9700272C2D /* LexModula.cxx in Sources */,
				28BA734924E34D9700272C2D /* LexASY.cxx in Sources */,
				28BA739024E34D9700272C2D /* LexVisualProlog.cxx in Sources */,
				28BA739524E34D9700272C2D /* LexPO.cxx in Sources */,
				28BA72BA24E34D5B00272C2D /* WordList.cxx in Sources */,
				28BA739624E34D9700272C2D /* LexPowerPro.cxx in Sources */,
				28BA733924E34D9700272C2D /* LexBasic.cxx in Sources */,
				28BA739D24E34D9700272C2D /* LexCaml.cxx in Sources */,
				28BA739724E34D9700272C2D /* LexProps.cxx in Sources */,
				28BA737424E34D9700272C2D /* LexDMIS.cxx in Sources */,
				28BA73A524E34D9700272C2D /* LexECL.cxx in Sources */,
				28BA736524E34D9700272C2D /* LexSQL.cxx in Sources */,
				28BA72AB24E34D5B00272C2D /* LexerBase.cxx in Sources */,
				28BA72B824E34D5B00272C2D /* DefaultLexer.cxx in Sources */,
				28BA73A024E34D9700272C2D /* LexTAL.cxx in Sources */,
				28BA733C24E34D9700272C2D /* LexMetapost.cxx in Sources */,
				28BA733A24E34D9700272C2D /* LexCIL.cxx in Sources */,
				28BA735D24E34D9700272C2D /* LexErrorList.cxx in Sources */,
				28BA737224E34D9700272C2D /* LexAda.cxx in Sources */,
				28BA737D24E34D9700272C2D /* LexAU3.cxx in Sources */,
				28BA734024E34D9700272C2D /* LexTACL.cxx in Sources */,
				28BA736724E34D9700272C2D /* LexEDIFACT.cxx in Sources */,
				28BA736024E34D9700272C2D /* LexAbaqus.cxx in Sources */,
				28BA734D24E34D9700272C2D /* LexAsn1.cxx in Sources */,
				28BA737A24E34D9700272C2D /* LexTeX.cxx in Sources */,
				28BA739124E34D9700272C2D /* LexVB.cxx in Sources */,
				28BA735E24E34D9700272C2D /* LexFlagship.cxx in Sources */,
				28BA735B24E34D9700272C2D /* LexRuby.cxx in Sources */,
				28BA735424E34D9700272C2D /* LexHollywood.cxx in Sources */,
				28BA736D24E34D9700272C2D /* LexFortran.cxx in Sources */,
				28BA738924E34D9700272C2D /* LexStata.cxx in Sources */,
				28BA737524E34D9700272C2D /* LexTCMD.cxx in Sources */,
				28BA72C624E34D5B00272C2D /* Accessor.cxx in Sources */,
				28BA733B24E34D9700272C2D /* LexTCL.cxx in Sources */,
				28BA739C24E34D9700272C2D /* LexMaxima.cxx in Sources */,
				28BA73AA24E34D9700272C2D /* LexRust.cxx in Sources */,
				28BA733F24E34D9700272C2D /* LexOScript.cxx in Sources */,
				28BA737324E34D9700272C2D /* LexCrontab.cxx in Sources */,
				28BA734E24E34D9700272C2D /* LexCoffeeScript.cxx in Sources */,
				28BA735624E34D9700272C2D /* LexLisp.cxx in Sources */,
				28BA735824E34D9700272C2D /* LexPS.cxx in Sources */,
				28BA735F24E34D9700272C2D /* LexLaTeX.cxx in Sources */,
				28BA736B24E34D9700272C2D /* LexSpecman.cxx in Sources */,
				28BA73A724E34D9700272C2D /* LexBibTeX.cxx in Sources */,
				28BA737E24E34D9700272C2D /* LexBaan.cxx in Sources */,
				28BA738124E34D9700272C2D /* LexTxt2tags.cxx in Sources */,
				28BA737F24E34D9700272C2D /* LexMPT.cxx in Sources */,
				28BA738424E34D9700272C2D /* LexSTTXT.cxx in Sources */,
				28BA734F24E34D9700272C2D /* LexDiff.cxx in Sources */,
				28BA735924E34D9700272C2D /* LexYAML.cxx in Sources */,
				28BA735524E34D9700272C2D /* LexProgress.cxx in Sources */,
				28BA736F24E34D9700272C2D /* LexPython.cxx in Sources */,
				28BA72B524E34D5B00272C2D /* CharacterSet.cxx in Sources */,
				28BA739E24E34D9700272C2D /* LexDataflex.cxx in Sources */,
				28BA738F24E34D9700272C2D /* LexScriptol.cxx in Sources */,
				28BA736C24E34D9700272C2D /* LexHTML.cxx in Sources */,
				28BA737924E34D9700272C2D /* LexMake.cxx in Sources */,
				28BA738524E34D9700272C2D /* LexMagik.cxx in Sources */,
				28BA72B124E34D5B00272C2D /* CharacterCategory.cxx in Sources */,
				28BA739424E34D9700272C2D /* LexPB.cxx in Sources */,
				28BA73A624E34D9700272C2D /* LexMatlab.cxx in Sources */,
				28BA736324E34D9700272C2D /* LexRaku.cxx in Sources */,
				28BA736224E34D9700272C2D /* LexCPP.cxx in Sources */,
				283A17AE2B47E61100DF5C82 /* InList.cxx in Sources */,
				28BA738A24E34D9700272C2D /* LexOpal.cxx in Sources */,
				28BA736E24E34D9700272C2D /* LexRegistry.cxx in Sources */,
				28BA738224E34D9700272C2D /* LexMMIXAL.cxx in Sources */,
				28BA736A24E34D9700272C2D /* LexKVIrc.cxx in Sources */,
				28BA73A224E34D9700272C2D /* LexJSON.cxx in Sources */,
				28BA738724E34D9700272C2D /* LexCsound.cxx in Sources */,
				28BA738824E34D9700272C2D /* LexLua.cxx in Sources */,
				28BA739824E34D9700272C2D /* LexCOBOL.cxx in Sources */,
				28BA73A824E34D9700272C2D /* LexNimrod.cxx in Sources */,
				28BA739324E34D9700272C2D /* LexAVS.cxx in Sources */,
				28BA737624E34D9700272C2D /* LexConf.cxx in Sources */,
				28BA734524E34D9700272C2D /* LexNim.cxx in Sources */,
				28BA73AE24E34DBC00272C2D /* Lexilla.cxx in Sources */,
				28BA72C324E34D5B00272C2D /* LexerSimple.cxx in Sources */,
				28BA735124E34D9700272C2D /* LexAPDL.cxx in Sources */,
				28BA736424E34D9700272C2D /* LexGAP.cxx in Sources */,
				28BA734324E34D9700272C2D /* LexRebol.cxx in Sources */,
				28BA733E24E34D9700272C2D /* LexSML.cxx in Sources */,
				28BA738C24E34D9700272C2D /* LexVerilog.cxx in Sources */,
				28BA738624E34D9700272C2D /* LexNull.cxx in Sources */,
				28BA736124E34D9700272C2D /* LexBatch.cxx in Sources */,
				28BA736924E34D9700272C2D /* LexPOV.cxx in Sources */,
				28BA734124E34D9700272C2D /* LexGui4Cli.cxx in Sources */,
				28BA734824E34D9700272C2D /* LexBullant.cxx in Sources */,
				28BA734B24E34D9700272C2D /* LexEiffel.cxx in Sources */,
				28BA73A424E34D9700272C2D /* LexAVE.cxx in Sources */,
				28BA738D24E34D9700272C2D /* LexHaskell.cxx in Sources */,
				28BA735024E34D9700272C2D /* LexSorcus.cxx in Sources */,
				28BA739F24E34D9700272C2D /* LexLout.cxx in Sources */,
				28BA73A124E34D9700272C2D /* LexMarkdown.cxx in Sources */,
				28BA739224E34D9700272C2D /* LexDMAP.cxx in Sources */,
				28BA737824E34D9700272C2D /* LexA68k.cxx in Sources */,
				28BA735A24E34D9700272C2D /* LexErlang.cxx in Sources */,
				28BA738B24E34D9700272C2D /* LexHex.cxx in Sources */,
				28BA735224E34D9700272C2D /* LexD.cxx in Sources */,
				28BA73A324E34D9700272C2D /* LexPascal.cxx in Sources */,
				28BA739924E34D9700272C2D /* LexPLM.cxx in Sources */,
				28BA735724E34D9700272C2D /* LexPowerShell.cxx in Sources */,
				28BA738324E34D9700272C2D /* LexKix.cxx in Sources */,
				28BA72B424E34D5B00272C2D /* PropSetSimple.cxx in Sources */,
				28BA737C24E34D9700272C2D /* LexX12.cxx in Sources */,
				B32D4A2A9CEC222A5140E99F /* LexFSharp.cxx in Sources */,
				70BF497C8D265026B77C97DA /* LexJulia.cxx in Sources */,
				510D44AFB91EE873E86ABDD4 /* LexAsciidoc.cxx in Sources */,
				00D544CC992062D2E3CD4BF6 /* LexGDScript.cxx in Sources */,
				3D044C4CA34C6FD7E58E0091 /* LexTOML.cxx in Sources */,
				14314DCD945FDA90481F0A0D /* LexTroff.cxx in Sources */,
				0DFB4F5F94B018794ADB389D /* LexDart.cxx in Sources */,
				CEC8496B8D9712E6EEDBC301 /* LexZig.cxx in Sources */,
				4A444CF5A75E52E2C5537328 /* LexNix.cxx in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		280262AE246DF655000DF3B8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					DEBUG,
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		280262AF246DF655000DF3B8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = NDEBUG;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
			};
			name = Release;
		};
		280262B1246DF655000DF3B8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 5.4.3;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 4F446KW87E;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				EXECUTABLE_PREFIX = lib;
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_ENABLE_CPP_RTTI = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					../../include,
					../../../scintilla/include,
					../../lexlib,
				);
				INFOPLIST_FILE = "$(SRCROOT)/Lexilla/Info.plist";
				INSTALL_PATH = "@rpath";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				PRODUCT_BUNDLE_IDENTIFIER = org.scintilla.Lexilla;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		280262B2246DF655000DF3B8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 5.4.3;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 4F446KW87E;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				EXECUTABLE_PREFIX = lib;
				GCC_ENABLE_CPP_EXCEPTIONS = YES;
				GCC_ENABLE_CPP_RTTI = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				HEADER_SEARCH_PATHS = (
					../../include,
					../../../scintilla/include,
					../../lexlib,
				);
				INFOPLIST_FILE = "$(SRCROOT)/Lexilla/Info.plist";
				INSTALL_PATH = "@rpath";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				PRODUCT_BUNDLE_IDENTIFIER = org.scintilla.Lexilla;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		280262A0246DF655000DF3B8 /* Build configuration list for PBXProject "Lexilla" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				280262AE246DF655000DF3B8 /* Debug */,
				280262AF246DF655000DF3B8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		280262B0246DF655000DF3B8 /* Build configuration list for PBXNativeTarget "lexilla" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				280262B1246DF655000DF3B8 /* Debug */,
				280262B2246DF655000DF3B8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2802629D246DF655000DF3B8 /* Project object */;
}
