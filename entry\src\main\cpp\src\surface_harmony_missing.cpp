#include "../include/platform_harmony.h"
#include <hilog/log.h>
#include <native_drawing/drawing_canvas.h>
#include <native_drawing/drawing_pen.h>
#include <native_drawing/drawing_brush.h>
#include <native_drawing/drawing_path.h>
#include <native_drawing/drawing_text_typography.h>

#define LOG_PRINT_DOMAIN 0xFF00
#undef LOG_TAG
#define LOG_TAG "SurfaceHarmonyMissing"

using namespace Scintilla;
using namespace Scintilla::Internal;

// SurfaceHarmony 缺失方法的实现

void SurfaceHarmony::Init(WindowID wid) {
    // 仅用于测量文本的初始化
    initialised_ = true;
}

void SurfaceHarmony::Init(SurfaceID sid, WindowID wid) {
    // 用于绘制的初始化
    canvas_ = static_cast<OH_Drawing_Canvas*>(sid);
    initialised_ = true;
}

std::unique_ptr<Surface> SurfaceHarmony::AllocatePixMap(int width, int height) {
    // 分配像素图
    auto surface = std::make_unique<SurfaceHarmony>();
    surface->width_ = width;
    surface->height_ = height;
    surface->initialised_ = true;
    return surface;
}

void SurfaceHarmony::SetMode(SurfaceMode mode) {
    // 设置表面模式
}

int SurfaceHarmony::SupportsFeature(Scintilla::Supports feature) noexcept {
    switch (feature) {
        case Scintilla::Supports::LineDrawsFinal:
        case Scintilla::Supports::PixelDivisions:
        case Scintilla::Supports::FractionalStrokeWidth:
        case Scintilla::Supports::TranslucentStroke:
        case Scintilla::Supports::PixelModification:
            return 1;
        default:
            return 0;
    }
}

int SurfaceHarmony::LogPixelsY() {
    return 96; // 标准DPI
}

int SurfaceHarmony::PixelDivisions() {
    return 1; // 不支持子像素
}

int SurfaceHarmony::DeviceHeightFont(int points) {
    return points * LogPixelsY() / 72;
}

void SurfaceHarmony::LineDraw(Point start, Point end, Stroke stroke) {
    if (!canvas_ || !pen_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "LineDraw: (%{public}f,%{public}f) to (%{public}f,%{public}f)",
                 start.x, start.y, end.x, end.y);

    // 简化实现
    SetPenColour(stroke.colour);
}

void SurfaceHarmony::PolyLine(const Point *pts, size_t npts, Stroke stroke) {
    if (!canvas_ || !pen_ || npts < 2) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "PolyLine: %{public}zu points", npts);

    // 简化实现
    SetPenColour(stroke.colour);
}

void SurfaceHarmony::RectangleFrame(PRectangle rc, Stroke stroke) {
    if (!canvas_ || !pen_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "RectangleFrame: %{public}f,%{public}f,%{public}f,%{public}f",
                 rc.left, rc.top, rc.right, rc.bottom);

    // 简化实现
    SetPenColour(stroke.colour);
}

void SurfaceHarmony::FillRectangleAligned(PRectangle rc, Fill fill) {
    // 对齐填充矩形，与普通填充相同
    FillRectangle(rc, fill);
}

void SurfaceHarmony::Stadium(PRectangle rc, FillStroke fillStroke, Ends ends) {
    // 体育场形状（圆角矩形的变体）
    if (!canvas_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Stadium: %{public}f,%{public}f,%{public}f,%{public}f",
                 rc.left, rc.top, rc.right, rc.bottom);

    // 简化实现
    SetBrushColour(fillStroke.fill.colour);
    SetPenColour(fillStroke.stroke.colour);
}

void SurfaceHarmony::PopClip() {
    // 弹出裁剪区域 - 简化实现
    if (canvas_) {
        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "PopClip");
    }
}

void SurfaceHarmony::FlushDrawing() {
    // 刷新绘制
}

// UTF8 文本绘制方法（委托给普通方法）
void SurfaceHarmony::DrawTextNoClipUTF8(PRectangle rc, const Font *font_, XYPOSITION ybase, 
                                       std::string_view text, ColourRGBA fore, ColourRGBA back) {
    DrawTextNoClip(rc, font_, ybase, text, fore, back);
}

void SurfaceHarmony::DrawTextClippedUTF8(PRectangle rc, const Font *font_, XYPOSITION ybase, 
                                        std::string_view text, ColourRGBA fore, ColourRGBA back) {
    DrawTextClipped(rc, font_, ybase, text, fore, back);
}

void SurfaceHarmony::DrawTextTransparentUTF8(PRectangle rc, const Font *font_, XYPOSITION ybase, 
                                            std::string_view text, ColourRGBA fore) {
    DrawTextTransparent(rc, font_, ybase, text, fore);
}

void SurfaceHarmony::MeasureWidthsUTF8(const Font *font_, std::string_view text, XYPOSITION *positions) {
    MeasureWidths(font_, text, positions);
}

XYPOSITION SurfaceHarmony::WidthTextUTF8(const Font *font_, std::string_view text) {
    return WidthText(font_, text);
}
