# Created by DepGen.py. To recreate, run DepGen.py.
$(DIR_O)/Lexilla.o: \
	../src/Lexilla.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../lexlib/LexerModule.h \
	../lexlib/CatalogueModules.h
$(DIR_O)/Accessor.o: \
	../lexlib/Accessor.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h
$(DIR_O)/CharacterCategory.o: \
	../lexlib/CharacterCategory.cxx \
	../lexlib/CharacterCategory.h
$(DIR_O)/CharacterSet.o: \
	../lexlib/CharacterSet.cxx \
	../lexlib/CharacterSet.h
$(DIR_O)/DefaultLexer.o: \
	../lexlib/DefaultLexer.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/LexerModule.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/InList.o: \
	../lexlib/InList.cxx \
	../lexlib/InList.h \
	../lexlib/CharacterSet.h
$(DIR_O)/LexAccessor.o: \
	../lexlib/LexAccessor.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../lexlib/LexAccessor.h \
	../lexlib/CharacterSet.h
$(DIR_O)/LexerBase.o: \
	../lexlib/LexerBase.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/LexerModule.h \
	../lexlib/LexerBase.h
$(DIR_O)/LexerModule.o: \
	../lexlib/LexerModule.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/LexerModule.h \
	../lexlib/LexerBase.h \
	../lexlib/LexerSimple.h
$(DIR_O)/LexerSimple.o: \
	../lexlib/LexerSimple.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/LexerModule.h \
	../lexlib/LexerBase.h \
	../lexlib/LexerSimple.h
$(DIR_O)/PropSetSimple.o: \
	../lexlib/PropSetSimple.cxx \
	../lexlib/PropSetSimple.h
$(DIR_O)/StyleContext.o: \
	../lexlib/StyleContext.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h
$(DIR_O)/WordList.o: \
	../lexlib/WordList.cxx \
	../lexlib/WordList.h \
	../lexlib/CharacterSet.h
$(DIR_O)/LexA68k.o: \
	../lexers/LexA68k.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAbaqus.o: \
	../lexers/LexAbaqus.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAda.o: \
	../lexers/LexAda.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAPDL.o: \
	../lexers/LexAPDL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAsciidoc.o: \
	../lexers/LexAsciidoc.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAsm.o: \
	../lexers/LexAsm.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexAsn1.o: \
	../lexers/LexAsn1.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexASY.o: \
	../lexers/LexASY.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAU3.o: \
	../lexers/LexAU3.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAVE.o: \
	../lexers/LexAVE.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexAVS.o: \
	../lexers/LexAVS.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexBaan.o: \
	../lexers/LexBaan.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexBash.o: \
	../lexers/LexBash.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/StringCopy.h \
	../lexlib/InList.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SubStyles.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexBasic.o: \
	../lexers/LexBasic.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexBatch.o: \
	../lexers/LexBatch.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/InList.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexBibTeX.o: \
	../lexers/LexBibTeX.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexBullant.o: \
	../lexers/LexBullant.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCaml.o: \
	../lexers/LexCaml.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCIL.o: \
	../lexers/LexCIL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/StringCopy.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexCLW.o: \
	../lexers/LexCLW.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCmake.o: \
	../lexers/LexCmake.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCOBOL.o: \
	../lexers/LexCOBOL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCoffeeScript.o: \
	../lexers/LexCoffeeScript.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexConf.o: \
	../lexers/LexConf.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCPP.o: \
	../lexers/LexCPP.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/StringCopy.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SparseState.h \
	../lexlib/SubStyles.h
$(DIR_O)/LexCrontab.o: \
	../lexers/LexCrontab.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCsound.o: \
	../lexers/LexCsound.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexCSS.o: \
	../lexers/LexCSS.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexD.o: \
	../lexers/LexD.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexDart.o: \
	../lexers/LexDart.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexDataflex.o: \
	../lexers/LexDataflex.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexDiff.o: \
	../lexers/LexDiff.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexDMAP.o: \
	../lexers/LexDMAP.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexDMIS.o: \
	../lexers/LexDMIS.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexECL.o: \
	../lexers/LexECL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h
$(DIR_O)/LexEDIFACT.o: \
	../lexers/LexEDIFACT.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/LexAccessor.h \
	../lexlib/LexerModule.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexEiffel.o: \
	../lexers/LexEiffel.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexErlang.o: \
	../lexers/LexErlang.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexErrorList.o: \
	../lexers/LexErrorList.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/InList.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexEScript.o: \
	../lexers/LexEScript.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexFlagship.o: \
	../lexers/LexFlagship.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexForth.o: \
	../lexers/LexForth.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexFortran.o: \
	../lexers/LexFortran.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexFSharp.o: \
	../lexers/LexFSharp.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexGAP.o: \
	../lexers/LexGAP.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexGDScript.o: \
	../lexers/LexGDScript.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/StringCopy.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/CharacterCategory.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SubStyles.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexGui4Cli.o: \
	../lexers/LexGui4Cli.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexHaskell.o: \
	../lexers/LexHaskell.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/CharacterCategory.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexHex.o: \
	../lexers/LexHex.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexHollywood.o: \
	../lexers/LexHollywood.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexHTML.o: \
	../lexers/LexHTML.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/InList.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SubStyles.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexIndent.o: \
	../lexers/LexIndent.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexInno.o: \
	../lexers/LexInno.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexJSON.o: \
	../lexers/LexJSON.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexJulia.o: \
	../lexers/LexJulia.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/StringCopy.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/CharacterCategory.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexKix.o: \
	../lexers/LexKix.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexKVIrc.o: \
	../lexers/LexKVIrc.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexLaTeX.o: \
	../lexers/LexLaTeX.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/DefaultLexer.h \
	../lexlib/LexerBase.h
$(DIR_O)/LexLisp.o: \
	../lexers/LexLisp.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexLout.o: \
	../lexers/LexLout.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexLua.o: \
	../lexers/LexLua.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SubStyles.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexMagik.o: \
	../lexers/LexMagik.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMake.o: \
	../lexers/LexMake.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMarkdown.o: \
	../lexers/LexMarkdown.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMatlab.o: \
	../lexers/LexMatlab.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMaxima.o: \
	../lexers/LexMaxima.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMetapost.o: \
	../lexers/LexMetapost.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMMIXAL.o: \
	../lexers/LexMMIXAL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexModula.o: \
	../lexers/LexModula.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMPT.o: \
	../lexers/LexMPT.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMSSQL.o: \
	../lexers/LexMSSQL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexMySQL.o: \
	../lexers/LexMySQL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexNim.o: \
	../lexers/LexNim.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/StringCopy.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexNimrod.o: \
	../lexers/LexNimrod.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexNix.o: \
	../lexers/LexNix.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexNsis.o: \
	../lexers/LexNsis.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexNull.o: \
	../lexers/LexNull.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexOpal.o: \
	../lexers/LexOpal.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexOScript.o: \
	../lexers/LexOScript.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPascal.o: \
	../lexers/LexPascal.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPB.o: \
	../lexers/LexPB.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPerl.o: \
	../lexers/LexPerl.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexPLM.o: \
	../lexers/LexPLM.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPO.o: \
	../lexers/LexPO.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPOV.o: \
	../lexers/LexPOV.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPowerPro.o: \
	../lexers/LexPowerPro.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPowerShell.o: \
	../lexers/LexPowerShell.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexProgress.o: \
	../lexers/LexProgress.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SparseState.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexProps.o: \
	../lexers/LexProps.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPS.o: \
	../lexers/LexPS.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexPython.o: \
	../lexers/LexPython.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/CharacterCategory.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SubStyles.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexR.o: \
	../lexers/LexR.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexRaku.o: \
	../lexers/LexRaku.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/CharacterCategory.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexRebol.o: \
	../lexers/LexRebol.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexRegistry.o: \
	../lexers/LexRegistry.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexRuby.o: \
	../lexers/LexRuby.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/InList.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SubStyles.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexRust.o: \
	../lexers/LexRust.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/PropSetSimple.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexSAS.o: \
	../lexers/LexSAS.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexScriptol.o: \
	../lexers/LexScriptol.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexSmalltalk.o: \
	../lexers/LexSmalltalk.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexSML.o: \
	../lexers/LexSML.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexSorcus.o: \
	../lexers/LexSorcus.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexSpecman.o: \
	../lexers/LexSpecman.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexSpice.o: \
	../lexers/LexSpice.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexSQL.o: \
	../lexers/LexSQL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SparseState.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexStata.o: \
	../lexers/LexStata.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexSTTXT.o: \
	../lexers/LexSTTXT.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTACL.o: \
	../lexers/LexTACL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTADS3.o: \
	../lexers/LexTADS3.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTAL.o: \
	../lexers/LexTAL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTCL.o: \
	../lexers/LexTCL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTCMD.o: \
	../lexers/LexTCMD.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTeX.o: \
	../lexers/LexTeX.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTOML.o: \
	../lexers/LexTOML.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTroff.o: \
	../lexers/LexTroff.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexTxt2tags.o: \
	../lexers/LexTxt2tags.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexVB.o: \
	../lexers/LexVB.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexVerilog.o: \
	../lexers/LexVerilog.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/SubStyles.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexVHDL.o: \
	../lexers/LexVHDL.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexVisualProlog.o: \
	../lexers/LexVisualProlog.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/CharacterCategory.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexX12.o: \
	../lexers/LexX12.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/LexerModule.h \
	../lexlib/DefaultLexer.h
$(DIR_O)/LexYAML.o: \
	../lexers/LexYAML.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h
$(DIR_O)/LexZig.o: \
	../lexers/LexZig.cxx \
	../../scintilla/include/ILexer.h \
	../../scintilla/include/Sci_Position.h \
	../../scintilla/include/Scintilla.h \
	../include/SciLexer.h \
	../lexlib/WordList.h \
	../lexlib/LexAccessor.h \
	../lexlib/Accessor.h \
	../lexlib/StyleContext.h \
	../lexlib/CharacterSet.h \
	../lexlib/LexerModule.h \
	../lexlib/OptionSet.h \
	../lexlib/DefaultLexer.h
