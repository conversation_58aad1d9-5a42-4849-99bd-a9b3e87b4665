// 定义全局快捷键事件
export type GlobalHotkeyFunctionEnum = 'copy' | 'cut' | 'del'

type  GlobalHotkeyEventType = {
  [K in GlobalHotkeyFunctionEnum]: (...agr)=>void
}

function copy(str:string){
  console.log('当前复制的字段为',str)
}
function cut(str:string){
  console.log('当前剪切的字段为',str)
}
function del(str:string){
  console.log('当前删除的字段为',str)
}

export const GlobalHotkeyEvent:GlobalHotkeyEventType = {
  "copy":copy,
  "cut":cut,
  "del":del
}