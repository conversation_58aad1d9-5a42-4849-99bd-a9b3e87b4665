#ifndef PLATFORM_HARMONY_H
#define PLATFORM_HARMONY_H

#include <cstddef>
#include <cstdio>
#include <string>
#include <string_view>
#include <vector>
#include <optional>
#include <memory>
#include <unordered_map>

#include "../src/scintilla/Debugging.h"
#include "../src/scintilla/Geometry.h"
#include "ScintillaTypes.h"
#include "ScintillaMessages.h"
#include "../src/scintilla/Platform.h"
#include <native_drawing/drawing_canvas.h>
#include <native_drawing/drawing_pen.h>
#include <native_drawing/drawing_brush.h>
#include <native_drawing/drawing_path.h>
#include <native_drawing/drawing_text_typography.h>

namespace Scintilla::Internal {

class FontHarmony : public Font {
public:
    FontHarmony(const FontParameters &fp);
    ~FontHarmony() override;

    void SetSize(int size);
    int GetSize() const { return size_; }

private:
    int size_;
    std::string family_;
    bool bold_;
    bool italic_;
    OH_Drawing_Typography* typography_;
};

class SurfaceHarmony : public Surface {
public:
    SurfaceHarmony();
    ~SurfaceHarmony() override;

    void Init(void* window, int width, int height);
    void Init(WindowID wid) override;
    void Init(SurfaceID sid, WindowID wid) override;
    std::unique_ptr<Surface> AllocatePixMap(int width, int height) override;
    void SetMode(SurfaceMode mode) override;
    void Release() noexcept override;
    int SupportsFeature(Scintilla::Supports feature) noexcept override;
    bool Initialised() override;
    int LogPixelsY() override;
    int PixelDivisions() override;
    int DeviceHeightFont(int points) override;
    void LineDraw(Point start, Point end, Stroke stroke) override;
    void PolyLine(const Point *pts, size_t npts, Stroke stroke) override;
    void PenColour(ColourRGBA fore);
    void BrushColour(ColourRGBA back);
    void SetClip(PRectangle rc) override;
    void PopClip() override;
    void FlushCachedState() override;
    void FlushDrawing() override;

    void LineTo(Point pt);
    void Polygon(const Point *pts, size_t npts, FillStroke fillStroke) override;
    void RectangleDraw(PRectangle rc, FillStroke fillStroke) override;
    void RectangleFrame(PRectangle rc, Stroke stroke) override;
    void FillRectangle(PRectangle rc, Fill fill) override;
    void FillRectangleAligned(PRectangle rc, Fill fill) override;
    void FillRectangle(PRectangle rc, Surface &surfacePattern) override;
    void RoundedRectangle(PRectangle rc, FillStroke fillStroke) override;
    void AlphaRectangle(PRectangle rc, XYPOSITION cornerSize, FillStroke fillStroke) override;
    void GradientRectangle(PRectangle rc, const std::vector<ColourStop> &stops, GradientOptions options) override;
    void DrawRGBAImage(PRectangle rc, int width, int height, const unsigned char *pixelsImage) override;
    void Ellipse(PRectangle rc, FillStroke fillStroke) override;
    void Stadium(PRectangle rc, FillStroke fillStroke, Ends ends) override;
    void Copy(PRectangle rc, Point from, Surface &surfaceSource) override;

    std::unique_ptr<IScreenLineLayout> Layout(const IScreenLine *screenLine) override;

    void DrawTextNoClip(PRectangle rc, const Font *font_, XYPOSITION ybase, std::string_view text, ColourRGBA fore, ColourRGBA back) override;
    void DrawTextClipped(PRectangle rc, const Font *font_, XYPOSITION ybase, std::string_view text, ColourRGBA fore, ColourRGBA back) override;
    void DrawTextTransparent(PRectangle rc, const Font *font_, XYPOSITION ybase, std::string_view text, ColourRGBA fore) override;
    void MeasureWidths(const Font *font_, std::string_view text, XYPOSITION *positions) override;
    XYPOSITION WidthText(const Font *font_, std::string_view text) override;

    void DrawTextNoClipUTF8(PRectangle rc, const Font *font_, XYPOSITION ybase, std::string_view text, ColourRGBA fore, ColourRGBA back) override;
    void DrawTextClippedUTF8(PRectangle rc, const Font *font_, XYPOSITION ybase, std::string_view text, ColourRGBA fore, ColourRGBA back) override;
    void DrawTextTransparentUTF8(PRectangle rc, const Font *font_, XYPOSITION ybase, std::string_view text, ColourRGBA fore) override;
    void MeasureWidthsUTF8(const Font *font_, std::string_view text, XYPOSITION *positions) override;
    XYPOSITION WidthTextUTF8(const Font *font_, std::string_view text) override;

    XYPOSITION Ascent(const Font *font_) override;
    XYPOSITION Descent(const Font *font_) override;
    XYPOSITION InternalLeading(const Font *font_) override;
    XYPOSITION Height(const Font *font_) override;
    XYPOSITION AverageCharWidth(const Font *font_) override;

    void SetUnicodeMode(bool unicodeMode_);
    void SetDBCSMode(int codePage);
    void SetBidiR2L(bool bidiR2L_);

private:
    OH_Drawing_Canvas* canvas_;
    OH_Drawing_Pen* pen_;
    OH_Drawing_Brush* brush_;
    bool initialised_;
    int width_;
    int height_;
    ColourRGBA penColour_;
    ColourRGBA brushColour_;

    void SetPenColour(ColourRGBA colour);
    void SetBrushColour(ColourRGBA colour);
    void DrawTextCommon(PRectangle rc, const Font *font_, XYPOSITION ybase,
                       std::string_view text, ColourRGBA fore, ColourRGBA back, bool clipped);
};

class PlatformHarmony : public SurfaceHarmony {
public:
    PlatformHarmony();
    ~PlatformHarmony();
};

}

#endif