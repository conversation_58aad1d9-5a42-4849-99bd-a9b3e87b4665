#include "../include/scintilla_harmony.h"
#include "../include/platform_harmony.h"
#include <native_drawing/drawing_text_typography.h>
#include <hilog/log.h>
#include <algorithm>
#include <cctype>

using namespace Scintilla;
using namespace Scintilla::Internal;

#define LOG_PRINT_DOMAIN 0xFF00
#define LOG_TAG "ScintillaHarmony"

ScintillaHarmony::ScintillaHarmony() : nativeWindow(nullptr) {
    surface = new PlatformHarmony();
    // 初始化编辑器
    Initialise();
}

ScintillaHarmony::~ScintillaHarmony(){
    Finalise();
    if (surface) {
        delete surface;
        surface = nullptr;
    }
}

void ScintillaHarmony::Init(OH_NativeXComponent* component, void* window){
    char idStr[OH_XCOMPONENT_ID_LEN_MAX + 1] = {};
    uint64_t idSize = OH_XCOMPONENT_ID_LEN_MAX + 1;

    OH_NativeXComponent_GetXComponentId(component, idStr, &idSize);
    int32_t ret = OH_NativeXComponent_GetXComponentSize(component, window, &width_, &height_);

    if (ret == OH_NATIVEXCOMPONENT_RESULT_SUCCESS) {
        nativeWindow = static_cast<OHNativeWindow*>(window);

        // 初始化绘制表面
        if (surface) {
            static_cast<SurfaceHarmony*>(surface)->Init(window, width_, height_);
        }

        // 设置编辑器大小
        ChangeSize();

        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG,
                     "ScintillaHarmony initialized with size: %{public}llu x %{public}llu",
                     width_, height_);
    } else {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Failed to get XComponent size");
    }
}

void ScintillaHarmony::Finalise(){
    // 销毁资源
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_PRINT_DOMAIN, LOG_TAG, "Finalising ScintillaHarmony");

    if (surface) {
        surface->Release();
    }

    ScintillaBase::Finalise();
}

void ScintillaHarmony::NotifyChange(){
    // 通知内容发生变化
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "Content changed");
}

void ScintillaHarmony::NotifyParent(NotificationData scn){
    // 将通用平台的信息转换为鸿蒙平台需要的信息
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Notify parent: code=%{public}d", static_cast<int>(scn.nmhdr.code));

    // 这里可以添加向JavaScript层发送通知的逻辑
}

void ScintillaHarmony::NotifyFocus(bool focus){
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Focus changed: %{public}s", focus ? "gained" : "lost");
}


sptr_t ScintillaHarmony::WndProc(Message iMessage, uptr_t wParam, sptr_t lParam)
{
	try {
		switch (iMessage) {

		case Message::SetIMEInteraction:
			// 鸿蒙平台IME交互设置
			OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "SetIMEInteraction");
			break;

		case Message::GrabFocus:
			// 获取焦点
			OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "GrabFocus");
			SetFocusState(true);
			break;

		case Message::GetDirectFunction:
			// 返回直接函数指针
			return reinterpret_cast<sptr_t>(DirectFunction);

		case Message::GetDirectStatusFunction:
			// 返回直接状态函数指针
			return reinterpret_cast<sptr_t>(DirectStatusFunction);

		case Message::GetDirectPointer:
			// 返回当前实例指针
			return reinterpret_cast<sptr_t>(this);

		case Message::SetRectangularSelectionModifier:
			// 设置矩形选择修饰符
			rectangularSelectionModifier = static_cast<int>(wParam);
			break;

		case Message::GetRectangularSelectionModifier:
			// 获取矩形选择修饰符
			return rectangularSelectionModifier;

		// 移除不存在的消息类型
		// case Message::Paint:
		// case Message::SetSize:
		// 这些消息在Scintilla中不存在，由其他机制处理

		default:
			return ScintillaBase::WndProc(iMessage, wParam, lParam);
		}
	} catch (std::bad_alloc &) {
		errorStatus = Status::BadAlloc;
	} catch (...) {
		errorStatus = Status::Failure;
	}
	return 0;
}

// 添加一些辅助方法
void ScintillaHarmony::Paint() {
    if (!surface || !surface->Initialised()) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "Paint failed: surface not initialized");
        return;
    }

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "Paint called");

    // 获取客户区矩形
    PRectangle rcClient = GetClientRectangle();

    // 调用Editor基类的Paint方法进行实际绘制
    Editor::Paint(surface, rcClient);

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "Paint completed");
}

void ScintillaHarmony::ChangeSize() {
    if (width_ > 0 && height_ > 0) {
        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                     "ChangeSize: %{public}llu x %{public}llu", width_, height_);

        try {
            // 设置编辑器大小
            PRectangle rc(0, 0, static_cast<XYPOSITION>(width_), static_cast<XYPOSITION>(height_));

            // 调用Editor基类的方法设置大小
            SetScrollBars();

            // 触发重绘
            InvalidateStyleRedraw();

            OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "ChangeSize completed");
        } catch (...) {
            OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG, "ChangeSize failed");
        }
    }
}

// 静态回调函数
sptr_t ScintillaHarmony::DirectFunction(sptr_t ptr, unsigned int iMessage, uptr_t wParam, sptr_t lParam) {
    ScintillaHarmony* sci = reinterpret_cast<ScintillaHarmony*>(ptr);
    return sci->WndProc(static_cast<Message>(iMessage), wParam, lParam);
}

sptr_t ScintillaHarmony::DirectStatusFunction(sptr_t ptr, unsigned int iMessage, uptr_t wParam, sptr_t lParam, int* pStatus) {
    ScintillaHarmony* sci = reinterpret_cast<ScintillaHarmony*>(ptr);
    sptr_t result = sci->WndProc(static_cast<Message>(iMessage), wParam, lParam);
    if (pStatus) {
        *pStatus = static_cast<int>(sci->errorStatus);
    }
    return result;
}

// 便利方法实现
void ScintillaHarmony::SetText(const char* text) {
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "SetText called with: %{public}s", text ? text : "null");

    sptr_t result = WndProc(Message::SetText, 0, reinterpret_cast<sptr_t>(text));

    // 强制重绘以显示新文本
    InvalidateStyleRedraw();
    Redraw();

    // 如果有绘制表面，立即进行绘制
    if (surface && surface->Initialised()) {
        Paint();
    }

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "SetText completed, result: %{public}ld", result);
}

void ScintillaHarmony::GetText(int length, char* text) {
    WndProc(Message::GetText, static_cast<uptr_t>(length), reinterpret_cast<sptr_t>(text));
}

Sci::Position ScintillaHarmony::Length() {
    return WndProc(Message::GetLength, 0, 0);
}

Sci::Position ScintillaHarmony::CurrentPosition() {
    return WndProc(Message::GetCurrentPos, 0, 0);
}

void ScintillaHarmony::InsertString(Sci::Position pos, const char* s, Sci::Position insertLength) {
    WndProc(Message::InsertText, static_cast<uptr_t>(pos), reinterpret_cast<sptr_t>(s));
}

void ScintillaHarmony::StartStyling(Sci::Position pos) {
    WndProc(Message::StartStyling, static_cast<uptr_t>(pos), 0);
}

void ScintillaHarmony::SetStyling(Sci::Position length, int style) {
    WndProc(Message::SetStyling, static_cast<uptr_t>(length), static_cast<sptr_t>(style));
}

void ScintillaHarmony::Redraw() {
    // 使用Editor基类的方法触发重绘
    Editor::Redraw();
}

void ScintillaHarmony::SetCanvas(void* canvas) {
    if (surface && canvas) {
        SurfaceHarmony* harmonySurface = static_cast<SurfaceHarmony*>(surface);
        harmonySurface->Init(canvas, nullptr);

        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Canvas set: %{public}p", canvas);

        // 触发重绘
        InvalidateStyleRedraw();
        Redraw();
    }
}

PRectangle ScintillaHarmony::GetClientRectangle() const {
    // 返回基于当前窗口大小的客户区矩形
    PRectangle rc(0, 0, static_cast<XYPOSITION>(width_), static_cast<XYPOSITION>(height_));

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "GetClientRectangle: %{public}f x %{public}f", rc.Width(), rc.Height());

    return rc;
}

// 实现Editor的纯虚函数
void ScintillaHarmony::SetVerticalScrollPos() {
    // 鸿蒙平台的垂直滚动条位置设置
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "SetVerticalScrollPos");
}

void ScintillaHarmony::SetHorizontalScrollPos() {
    // 鸿蒙平台的水平滚动条位置设置
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "SetHorizontalScrollPos");
}

bool ScintillaHarmony::ModifyScrollBars(Sci::Line nMax, Sci::Line nPage) {
    // 鸿蒙平台的滚动条修改
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "ModifyScrollBars");
    return true;
}

void ScintillaHarmony::Copy() {
    // 鸿蒙平台的复制操作
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "Copy");
}

void ScintillaHarmony::Paste() {
    // 鸿蒙平台的粘贴操作
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "Paste");
}

void ScintillaHarmony::ClaimSelection() {
    // 鸿蒙平台的选择声明
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "ClaimSelection");
}

void ScintillaHarmony::CopyToClipboard(const SelectionText &selectedText) {
    // 鸿蒙平台的剪贴板复制
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "CopyToClipboard");
}

void ScintillaHarmony::SetMouseCapture(bool on) {
    // 鸿蒙平台的鼠标捕获
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "SetMouseCapture: %s", on ? "true" : "false");
}

bool ScintillaHarmony::HaveMouseCapture() {
    // 鸿蒙平台的鼠标捕获状态
    return false;
}

std::string ScintillaHarmony::UTF8FromEncoded(std::string_view encoded) const {
    // 简单的UTF8转换实现
    return std::string(encoded);
}

std::string ScintillaHarmony::EncodedFromUTF8(std::string_view utf8) const {
    // 简单的UTF8编码实现
    return std::string(utf8);
}

sptr_t ScintillaHarmony::DefWndProc(Scintilla::Message iMessage, uptr_t wParam, sptr_t lParam) {
    // 默认窗口过程
    return 0;
}

// 实现ScintillaBase的纯虚函数
void ScintillaHarmony::CreateCallTipWindow(PRectangle rc) {
    // 鸿蒙平台的调用提示窗口创建
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "CreateCallTipWindow");
}

void ScintillaHarmony::AddToPopUp(const char *label, int cmd, bool enabled) {
    // 鸿蒙平台的弹出菜单添加
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "AddToPopUp: %s", label);
}

// 实现定时器相关方法
bool ScintillaHarmony::FineTickerRunning(TickReason reason) {
    // 鸿蒙平台暂时不支持定时器，返回false
    return false;
}

void ScintillaHarmony::FineTickerStart(TickReason reason, int millis, int tolerance) {
    // 鸿蒙平台暂时不支持定时器，空实现
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "FineTickerStart: reason=%d, millis=%d", static_cast<int>(reason), millis);
}

void ScintillaHarmony::FineTickerCancel(TickReason reason) {
    // 鸿蒙平台暂时不支持定时器，空实现
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "FineTickerCancel: reason=%d", static_cast<int>(reason));
}

// 实现其他可能需要的方法
bool ScintillaHarmony::SetIdle(bool on) {
    // 鸿蒙平台的空闲状态设置
    return true;
}

void ScintillaHarmony::StartDrag() {
    // 鸿蒙平台的拖拽开始
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "StartDrag");
}

bool ScintillaHarmony::DragThreshold(Point ptStart, Point ptNow) {
    // 简单的拖拽阈值检测
    const int threshold = 3;
    return (abs(ptNow.x - ptStart.x) > threshold) || (abs(ptNow.y - ptStart.y) > threshold);
}

bool ScintillaHarmony::ValidCodePage(int codePage) const {
    // 支持UTF-8和默认代码页
    return (codePage == 0) || (codePage == 65001); // 65001 is UTF-8
}

std::unique_ptr<CaseFolder> ScintillaHarmony::CaseFolderForEncoding() {
    // 返回UTF-8的大小写转换器
    return std::make_unique<CaseFolderUnicode>();
}

std::string ScintillaHarmony::CaseMapString(const std::string &s, CaseMapping caseMapping) {
    // 简单的大小写映射实现
    std::string result = s;
    if (caseMapping == CaseMapping::upper) {
        std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    } else if (caseMapping == CaseMapping::lower) {
        std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    }
    return result;
}