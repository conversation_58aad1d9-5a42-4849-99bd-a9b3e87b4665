import { NodeContent } from "@kit.ArkUI";
// 导出函数声明
export const add: (a: number, b: number) => number;
export const createNativeNode: (content: NodeContent) => void;
export const SetText: (fd: number) => void;
export const createScintillaEditor: () => number;
export const initScintillaEditor: (editorId: number, xcomponent: any) => void;
export const destroyScintillaEditor: (editorId: number) => void;