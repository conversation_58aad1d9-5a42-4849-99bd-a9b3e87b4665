// 编写语言
@ComponentV2
export default struct WritingLanguage{
  @Local selectValue:string = 'Text'
  @Local selectIndex:number = 0
  @Param@Once selectDatasource:SelectOption[] =[]
  build() {
    Row(){
      Select(this.selectDatasource)
        .width(100)
        .selected(this.selectIndex!!)
        .value(this.selectValue!!)
        .onSelect((index,value)=>{
          this.selectValue = value;
          this.selectIndex = index
        })
    }
  }
}