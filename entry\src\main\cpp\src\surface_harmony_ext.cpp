#include "../include/platform_harmony.h"
#include <hilog/log.h>
#include <native_drawing/drawing_canvas.h>
#include <native_drawing/drawing_pen.h>
#include <native_drawing/drawing_brush.h>
#include <native_drawing/drawing_path.h>
#include <native_drawing/drawing_text_typography.h>

#define LOG_PRINT_DOMAIN 0xFF00
#undef LOG_TAG
#define LOG_TAG "SurfaceHarmonyExt"

using namespace Scintilla;
using namespace Scintilla::Internal;

// SurfaceHarmony 扩展方法实现

void SurfaceHarmony::AlphaRectangle(PRectangle rc, XYPOSITION cornerSize, FillStroke fillStroke) {
    if (!canvas_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "AlphaRectangle: %{public}f,%{public}f,%{public}f,%{public}f corner=%{public}f",
                 rc.left, rc.top, rc.right, rc.bottom, cornerSize);

    // 简化实现
    SetBrushColour(fillStroke.fill.colour);
    SetPenColour(fillStroke.stroke.colour);
}

void SurfaceHarmony::GradientRectangle(PRectangle rc, const std::vector<ColourStop> &stops,
                                      GradientOptions options) {
    // 渐变矩形实现 - 鸿蒙平台的渐变支持
    if (!canvas_ || stops.empty()) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "GradientRectangle with %{public}zu stops", stops.size());

    // 暂时用纯色填充，后续可以实现真正的渐变
    Fill fill(stops[0].colour);
    FillRectangle(rc, fill);
}

void SurfaceHarmony::DrawRGBAImage(PRectangle rc, int width, int height,
                                  const unsigned char *pixelsImage) {
    if (!canvas_ || !pixelsImage) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "DrawRGBAImage: %{public}dx%{public}d", width, height);

    // 简化实现 - 暂时不绘制图像，避免API问题
}

void SurfaceHarmony::Ellipse(PRectangle rc, FillStroke fillStroke) {
    if (!canvas_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Ellipse: %{public}f,%{public}f,%{public}f,%{public}f",
                 rc.left, rc.top, rc.right, rc.bottom);

    // 简化实现
    SetBrushColour(fillStroke.fill.colour);
    SetPenColour(fillStroke.stroke.colour);
}

void SurfaceHarmony::Copy(PRectangle rc, Point from, Surface &surfaceSource) {
    // 从其他表面复制内容
    // 暂时不实现，需要更复杂的位图操作
}

std::unique_ptr<IScreenLineLayout> SurfaceHarmony::Layout(const IScreenLine *screenLine) {
    // 文本布局实现
    // 返回空指针，使用默认布局
    return nullptr;
}

void SurfaceHarmony::DrawTextNoClip(PRectangle rc, const Font *font_, XYPOSITION ybase, 
                                   std::string_view text, ColourRGBA fore, ColourRGBA back) {
    DrawTextCommon(rc, font_, ybase, text, fore, back, false);
}

void SurfaceHarmony::DrawTextClipped(PRectangle rc, const Font *font_, XYPOSITION ybase, 
                                    std::string_view text, ColourRGBA fore, ColourRGBA back) {
    DrawTextCommon(rc, font_, ybase, text, fore, back, true);
}

void SurfaceHarmony::DrawTextTransparent(PRectangle rc, const Font *font_, XYPOSITION ybase,
                                        std::string_view text, ColourRGBA fore) {
    DrawTextCommon(rc, font_, ybase, text, fore, ColourRGBA(0, 0, 0, 0), false);
}

void SurfaceHarmony::DrawTextCommon(PRectangle rc, const Font *font_, XYPOSITION ybase,
                                   std::string_view text, ColourRGBA fore, ColourRGBA back, bool clipped) {
    if (!canvas_ || text.empty()) {
        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                     "DrawTextCommon skipped: canvas=%{public}p, text_empty=%{public}d",
                     canvas_, text.empty() ? 1 : 0);
        return;
    }

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "DrawTextCommon: '%{public}s' at (%{public}f,%{public}f) size=%{public}f x %{public}f",
                 std::string(text).c_str(), rc.left, ybase, rc.Width(), rc.Height());

    // 背景填充
    if (back.IsOpaque()) {
        Fill fill(back);
        FillRectangle(rc, fill);
        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                     "Background filled with color: R=%{public}d G=%{public}d B=%{public}d A=%{public}d",
                     back.GetRed(), back.GetGreen(), back.GetBlue(), back.GetAlpha());
    }

    // 文本绘制
    if (fore.IsOpaque() && brush_) {
        // 设置文本颜色
        SetBrushColour(fore);

        // 尝试使用鸿蒙绘制API绘制文本
        try {
            // 创建文本样式
            OH_Drawing_TypographyStyle* typoStyle = OH_Drawing_CreateTypographyStyle();
            if (typoStyle) {
                // 设置文本方向和对齐
                OH_Drawing_SetTypographyTextDirection(typoStyle, TEXT_DIRECTION_LTR);
                OH_Drawing_SetTypographyTextAlign(typoStyle, TEXT_ALIGN_START);

                // 创建文本构建器
                OH_Drawing_TypographyCreate* builder = OH_Drawing_CreateTypographyHandler(typoStyle, nullptr);
                if (builder) {
                    // 创建文本样式
                    OH_Drawing_TextStyle* textStyle = OH_Drawing_CreateTextStyle();
                    if (textStyle) {
                        // 设置字体大小和颜色
                        if (font_) {
                            const FontHarmony* harmonyFont = static_cast<const FontHarmony*>(font_);
                            OH_Drawing_SetTextStyleFontSize(textStyle, harmonyFont->GetSize());
                        } else {
                            OH_Drawing_SetTextStyleFontSize(textStyle, 12);
                        }

                        uint32_t color = (fore.GetAlpha() << 24) | (fore.GetRed() << 16) |
                                       (fore.GetGreen() << 8) | fore.GetBlue();
                        OH_Drawing_SetTextStyleColor(textStyle, color);

                        // 添加文本
                        std::string textStr(text);
                        OH_Drawing_TypographyHandlerPushTextStyle(builder, textStyle);
                        OH_Drawing_TypographyHandlerAddText(builder, textStr.c_str());
                        OH_Drawing_TypographyHandlerPopTextStyle(builder);

                        // 构建排版
                        OH_Drawing_Typography* typography = OH_Drawing_CreateTypography(builder);
                        if (typography) {
                            // 布局文本
                            OH_Drawing_TypographyLayout(typography, rc.Width());

                            // 绘制文本
                            OH_Drawing_TypographyPaint(typography, canvas_, rc.left, ybase);

                            OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                                         "Text painted successfully: '%{public}s'", textStr.c_str());

                            // 清理资源
                            OH_Drawing_DestroyTypography(typography);
                        }

                        OH_Drawing_DestroyTextStyle(textStyle);
                    }

                    OH_Drawing_DestroyTypographyHandler(builder);
                }

                OH_Drawing_DestroyTypographyStyle(typoStyle);
            }
        } catch (...) {
            OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG,
                         "Exception in text drawing, falling back to simple implementation");

            // 简单的矩形填充作为文本占位符
            Fill textFill(fore);
            PRectangle textRect(rc.left, ybase - 10, rc.left + text.length() * 8, ybase + 2);
            FillRectangle(textRect, textFill);
        }
    }
}

void SurfaceHarmony::MeasureWidths(const Font *font_, std::string_view text, XYPOSITION *positions) {
    if (!positions || text.empty()) return;
    
    // 简单实现：假设等宽字体
    XYPOSITION charWidth = AverageCharWidth(font_);
    for (size_t i = 0; i < text.length(); i++) {
        positions[i] = charWidth * (i + 1);
    }
}

XYPOSITION SurfaceHarmony::WidthText(const Font *font_, std::string_view text) {
    if (text.empty()) return 0;
    
    // 简单实现：字符数 * 平均字符宽度
    return text.length() * AverageCharWidth(font_);
}

XYPOSITION SurfaceHarmony::Ascent(const Font *font_) {
    if (font_) {
        const FontHarmony* harmonyFont = static_cast<const FontHarmony*>(font_);
        return harmonyFont->GetSize() * 0.8; // 大约80%的字体大小
    }
    return 10;
}

XYPOSITION SurfaceHarmony::Descent(const Font *font_) {
    if (font_) {
        const FontHarmony* harmonyFont = static_cast<const FontHarmony*>(font_);
        return harmonyFont->GetSize() * 0.2; // 大约20%的字体大小
    }
    return 2;
}

XYPOSITION SurfaceHarmony::InternalLeading(const Font *font_) {
    return 0; // 内部行距
}

XYPOSITION SurfaceHarmony::Height(const Font *font_) {
    return Ascent(font_) + Descent(font_);
}

XYPOSITION SurfaceHarmony::AverageCharWidth(const Font *font_) {
    if (font_) {
        const FontHarmony* harmonyFont = static_cast<const FontHarmony*>(font_);
        return harmonyFont->GetSize() * 0.6; // 大约60%的字体大小
    }
    return 7;
}

void SurfaceHarmony::SetUnicodeMode(bool unicodeMode_) {
    // 设置Unicode模式
}

void SurfaceHarmony::SetDBCSMode(int codePage) {
    // 设置双字节字符集模式
}

void SurfaceHarmony::SetBidiR2L(bool bidiR2L_) {
    // 设置从右到左的文本方向
}
