#include "../include/scintilla/Scintilla.h"
#include "./scintilla/XPM.h"
#include "./scintilla/UniConversion.h"
#include "./scintilla/DBCS.h"
#include "../include/platform_harmony.h"
#include <hilog/log.h>
#include <native_drawing/drawing_canvas.h>
#include <native_drawing/drawing_pen.h>
#include <native_drawing/drawing_brush.h>
#include <native_drawing/drawing_path.h>
#include <native_drawing/drawing_text_typography.h>

using namespace Scintilla;

#define LOG_PRINT_DOMAIN 0xFF00
#define LOG_TAG "ScintillaHarmony"

namespace Scintilla::Internal {

// Window 实现
Window::~Window() noexcept = default;

void Window::Destroy() noexcept {
    // 鸿蒙平台窗口销毁逻辑
}

PRectangle Window::GetPosition() const {
    // 返回窗口位置，暂时返回默认值
    return PRectangle(0, 0, 800, 600);
}

void Window::SetPosition(PRectangle rc) {
    // 设置窗口位置
}

void Window::SetPositionRelative(PRectangle rc, const Window *relativeTo) {
    // 相对于其他窗口设置位置
}

PRectangle Window::GetClientPosition() const {
    // 返回客户区位置
    return GetPosition();
}

void Window::Show(bool show) {
    // 显示/隐藏窗口
}

void Window::InvalidateAll() {
    // 使整个窗口无效，需要重绘
}

void Window::InvalidateRectangle(PRectangle rc) {
    // 使指定矩形区域无效
}

void Window::SetCursor(Cursor curs) {
    // 设置鼠标光标
}

PRectangle Window::GetMonitorRect(Point pt) {
    // 返回显示器矩形区域
    return PRectangle(0, 0, 1920, 1080);
}

// Platform 实现
ColourRGBA Platform::Chrome() {
    return ColourRGBA(0xF0, 0xF0, 0xF0, 0xFF); // 浅灰色
}

ColourRGBA Platform::ChromeHighlight() {
    return ColourRGBA(0x00, 0x78, 0xD4, 0xFF); // 蓝色高亮
}

const char *Platform::DefaultFont() {
    return "HarmonyOS Sans"; // 鸿蒙系统默认字体
}

int Platform::DefaultFontSize() {
    return 12; // 默认字体大小
}

unsigned int Platform::DoubleClickTime() {
    return 500; // 双击时间间隔（毫秒）
}

void Platform::Assert(const char *c, const char *file, int line) noexcept {
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Assert failed: %{public}s at %{public}s:%{public}d", c, file, line);
}

void Platform::DebugPrintf(const char *format, ...) noexcept {
    va_list args;
    va_start(args, format);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG, "%{public}s", buffer);
}

// Surface 分配器
std::unique_ptr<Surface> Surface::Allocate([[maybe_unused]] Technology technology) {
    return std::make_unique<SurfaceHarmony>();
}

// ListBox 分配器
std::unique_ptr<ListBox> ListBox::Allocate() {
    // 暂时返回空指针，后续可以实现鸿蒙平台的列表框
    return nullptr;
}

// Font 分配器
std::shared_ptr<Font> Font::Allocate(const FontParameters &fp) {
    return std::make_shared<FontHarmony>(fp);
}

// Menu 实现
Menu::Menu() noexcept : mid{} {
}

void Menu::CreatePopUp() {
    // 创建弹出菜单
}

void Menu::Destroy() noexcept {
    // 销毁菜单
}

void Menu::Show(Point pt, const Window &w) {
    // 显示菜单
}

// FontHarmony 实现
FontHarmony::FontHarmony(const FontParameters &fp)
    : size_(fp.size), family_(fp.faceName),
      bold_(static_cast<int>(fp.weight) > 500), italic_(fp.italic) {
    // 创建鸿蒙字体 - 暂时设为nullptr，后续实现
    typography_ = nullptr;
}

FontHarmony::~FontHarmony() {
    if (typography_) {
        // OH_Drawing_DestroyTypography(typography_);
    }
}

void FontHarmony::SetSize(int size) {
    size_ = size;
}

// SurfaceHarmony 实现
SurfaceHarmony::SurfaceHarmony()
    : canvas_(nullptr), pen_(nullptr), brush_(nullptr), initialised_(false),
      width_(0), height_(0), penColour_(0, 0, 0), brushColour_(255, 255, 255) {
}

SurfaceHarmony::~SurfaceHarmony() {
    Release();
}

void SurfaceHarmony::Init(void* window, int width, int height) {
    width_ = width;
    height_ = height;

    // 创建绘制对象
    pen_ = OH_Drawing_PenCreate();
    brush_ = OH_Drawing_BrushCreate();

    initialised_ = true;
}

void SurfaceHarmony::Release() noexcept {
    if (pen_) {
        OH_Drawing_PenDestroy(pen_);
        pen_ = nullptr;
    }
    if (brush_) {
        OH_Drawing_BrushDestroy(brush_);
        brush_ = nullptr;
    }
    initialised_ = false;
}

bool SurfaceHarmony::Initialised() {
    return initialised_;
}

void SurfaceHarmony::PenColour(ColourRGBA fore) {
    penColour_ = fore;
    SetPenColour(fore);
}

void SurfaceHarmony::BrushColour(ColourRGBA back) {
    brushColour_ = back;
    SetBrushColour(back);
}

void SurfaceHarmony::SetPenColour(ColourRGBA colour) {
    if (pen_) {
        uint32_t argb = (colour.GetAlpha() << 24) | (colour.GetRed() << 16) |
                       (colour.GetGreen() << 8) | colour.GetBlue();
        OH_Drawing_PenSetColor(pen_, argb);
    }
}

void SurfaceHarmony::SetBrushColour(ColourRGBA colour) {
    if (brush_) {
        uint32_t argb = (colour.GetAlpha() << 24) | (colour.GetRed() << 16) |
                       (colour.GetGreen() << 8) | colour.GetBlue();
        OH_Drawing_BrushSetColor(brush_, argb);
    }
}

void SurfaceHarmony::SetClip(PRectangle rc) {
    // 设置裁剪区域 - 简化实现
    if (canvas_) {
        // 暂时不实现裁剪，避免API问题
        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                     "SetClip: %{public}f,%{public}f,%{public}f,%{public}f",
                     rc.left, rc.top, rc.right, rc.bottom);
    }
}

void SurfaceHarmony::FlushCachedState() {
    // 刷新缓存状态
}

void SurfaceHarmony::LineTo(Point pt) {
    // 画线到指定点 - 简化实现
    if (canvas_ && pen_) {
        OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                     "LineTo: %{public}f,%{public}f", pt.x, pt.y);
        // 暂时不实现具体绘制，避免API问题
    }
}

void SurfaceHarmony::Polygon(const Point *pts, size_t npts, FillStroke fillStroke) {
    if (!canvas_ || npts < 3) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "Polygon: %{public}zu points", npts);

    // 简化实现 - 暂时只记录日志
    SetBrushColour(fillStroke.fill.colour);
    SetPenColour(fillStroke.stroke.colour);
}

void SurfaceHarmony::RectangleDraw(PRectangle rc, FillStroke fillStroke) {
    if (!canvas_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "RectangleDraw: %{public}f,%{public}f,%{public}f,%{public}f",
                 rc.left, rc.top, rc.right, rc.bottom);

    // 简化实现
    SetBrushColour(fillStroke.fill.colour);
    SetPenColour(fillStroke.stroke.colour);
}

void SurfaceHarmony::FillRectangle(PRectangle rc, Fill fill) {
    if (!canvas_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "FillRectangle: %{public}f,%{public}f,%{public}f,%{public}f",
                 rc.left, rc.top, rc.right, rc.bottom);

    // 简化实现
    SetBrushColour(fill.colour);
}

void SurfaceHarmony::FillRectangle(PRectangle rc, Surface &surfacePattern) {
    // 使用图案填充矩形 - 简化实现
    if (!canvas_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "FillRectangle with pattern");
}

void SurfaceHarmony::RoundedRectangle(PRectangle rc, FillStroke fillStroke) {
    if (!canvas_) return;

    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_PRINT_DOMAIN, LOG_TAG,
                 "RoundedRectangle: %{public}f,%{public}f,%{public}f,%{public}f",
                 rc.left, rc.top, rc.right, rc.bottom);

    // 简化实现
    SetBrushColour(fillStroke.fill.colour);
    SetPenColour(fillStroke.stroke.colour);
}

// PlatformHarmony 实现
PlatformHarmony::PlatformHarmony() : SurfaceHarmony() {
}

PlatformHarmony::~PlatformHarmony() {
}

}