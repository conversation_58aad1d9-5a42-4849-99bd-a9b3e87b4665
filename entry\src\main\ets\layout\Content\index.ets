import { common } from "@kit.AbilityKit";
import { picker } from "@kit.CoreFileKit";
import { fileIo as fs } from '@kit.CoreFileKit';
import { buffer, util } from "@kit.ArkTS";
import { CommonConstant as Common } from 'ets/commons/CommonConstant'
import { NodeContent } from "@kit.ArkUI";
import libentry from "libentry.so"

interface cursorPositionInterface {
  row: number;
  column: number;
}

interface selectedInterface {
  selectNumber: number;
  column: number;
}

@ComponentV2
export default struct EditContent {
  private nodeContent: NodeContent = new NodeContent(); // 内容插槽，用于显示Native XComponent
  @Local selectValue:string = 'Text'
  @Local selectIndex:number = 0
  @Local arrayBuffer:ArrayBuffer = new ArrayBuffer(1024 * 1024);
  @Local fileLength:number = 0 ; // 文件长度
  @Local fileRow:number = 0; //文件行数
  @Local cursorPosition: cursorPositionInterface = {row:0,column:0} // 光标位置
  @Local selected: selectedInterface = {selectNumber:0,column:0} // 选中的字符数和行数
  @Local character:string = 'UTF-8' // 当前文件的字符集
  @Local editMode:'ins'|'ove' = 'ins' // 当前编辑模式
  @Local editorId: number = -1 // Scintilla编辑器ID
  @Local selectDatasource:SelectOption[] =[
    {
      value:'Text',
    },
    {
      value:'Java',

    },
    {
      value:'JavaScript',
    },
    {
      value:'C',
    },
    {
      value:'C++',
    }
  ]
  aboutToAppear(): void {
    // 创建Native XComponent节点
    libentry.createNativeNode(this.nodeContent)

    // 创建Scintilla编辑器实例
    this.editorId = libentry.createScintillaEditor()
    console.log('Created Scintilla editor with ID:', this.editorId)

    // 延迟初始化编辑器，等待XComponent准备就绪
    setTimeout(() => {
      if (this.editorId > 0) {
        // 这里需要获取XComponent的native指针，暂时跳过
        // libentry.initScintillaEditor(this.editorId, xcomponentPointer)
        console.log('Editor initialization deferred')
      }
    }, 100)
  }

  aboutToDisappear(): void {
    // 清理编辑器实例
    if (this.editorId > 0) {
      libentry.destroyScintillaEditor(this.editorId)
      console.log('Destroyed Scintilla editor with ID:', this.editorId)
      this.editorId = -1
    }
  }


  // 选择单个文件并打开
  async  openFile(){
    let uris: Array<string> = []
    let context = getContext(this) as common.Context; // 请确保 getContext(this) 返回结果为 UIAbilityContext
    const documentViewPicker = new picker.DocumentViewPicker(context);
    const documentSelectOptions = new picker.DocumentSelectOptions();
    // 读取最大文件数
    documentSelectOptions.maxSelectNumber = 1;
    documentSelectOptions.fileSuffixFilters = ['文档(.txt,.md,.csv,.docx,.pptx,.pdf,.py,.java,.html,.json,.js,.ts,.tsx,.jsx)|.txt,.md,.csv,.docx,.pptx,.pdf,.py,.java,.html,.json,.js,.ts,.tsx,.jsx']
    // 文件选择配置
    documentSelectOptions.mergeMode = picker.MergeTypeMode.DEFAULT;
    uris = await documentViewPicker.select(documentSelectOptions)
    const file:fs.File = await fs.open(uris[0])
    // this.fileRow = await fs.read(file.fd,this.arrayBuffer)
    // let buf = buffer.from(this.arrayBuffer,0,this.fileRow)
    // console.log('str',file.path)
    // 传递文件句柄至
    libentry.SetText(file.fd)
  }

  build() {
    Column(){
      Row({space:10}){
        Button('打开文件')
          .onClick(()=>{this.openFile()})
        Select(this.selectDatasource)
          .width(100)
          .selected(this.selectIndex!!)
          .value(this.selectValue!!)
          .onSelect((index,value)=>{
            this.selectValue = value;
            this.selectIndex = index
          })
      }
      .padding({left:20,top:10,right:20,bottom:10})
      .height(60)
      .width('100%')
      .border({width:'1vp',color:Color.Black,radius:'2vp'})

      // 内容区,使用contentSlot渲染
      Column(){
        ContentSlot(this.nodeContent);
      }
      .height('calc(100% - 100vp)')
      .width('100%')

      // 底部显示栏
      Row(){
        Text(this.selectValue)
      }
      .height(40)
      .width('100%')
      .border({width:'1vp',color:Color.Black,radius:'2vp'})
    }
    .width('100%')
    .height('100%')
  }
}

