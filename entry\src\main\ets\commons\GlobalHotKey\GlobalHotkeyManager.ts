import { inputConsumer, KeyCode,KeyEvent  } from "@kit.InputKit";
import {GlobalHotkeyFunctionEnum} from './GlobalHotkeyEvent'

export  interface GlobalHoyKeyInterface {
  hotkey:inputConsumer.HotkeyOptions
  function:GlobalHotkeyFunctionEnum
}
let leftCtrlKey = 2072;
// 全局快捷键集合
export const GlobalHotkeyManager:GlobalHoyKeyInterface[]= [
  {hotkey:{preKeys:[leftCtrlKey],finalKey:KeyCode.KEYCODE_C},function:'copy'},
  {hotkey:{preKeys:[leftCtrlKey],finalKey:KeyCode.KEYCODE_X},function:'cut'},
]