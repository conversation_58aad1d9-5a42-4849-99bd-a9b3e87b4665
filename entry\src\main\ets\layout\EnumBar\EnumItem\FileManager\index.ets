// 文件管理类操作
import  { picker } from '@kit.CoreFileKit';
import { fileIo as fs } from '@kit.CoreFileKit';
import { common } from '@kit.AbilityKit';

@ComponentV2
export default struct FileManager{
  // 选择单个文件并打开
  async  openFile(){
    let uris: Array<string> = []
    let context = getContext(this) as common.Context; // 请确保 getContext(this) 返回结果为 UIAbilityContext
    const documentViewPicker = new picker.DocumentViewPicker(context);
    const documentSelectOptions = new picker.DocumentSelectOptions();
    documentSelectOptions.maxSelectNumber = 1;
    documentSelectOptions.mergeMode = picker.MergeTypeMode.DEFAULT;
    uris = await documentViewPicker.select(documentSelectOptions)
    console.log('uris[0]',uris[0])
    const file:fs.File = await fs.open(uris[0])
    console.log('file--------->',file.name);
  }
  build() {
    Row(){
      Button('打开文件')
        .onClick(()=>{this.openFile()})
    }
  }
}