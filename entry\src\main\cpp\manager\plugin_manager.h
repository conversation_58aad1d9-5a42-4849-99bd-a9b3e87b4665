/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef NATIVE_XCOMPONENT_PLUGIN_MANAGER_H
#define NATIVE_XCOMPONENT_PLUGIN_MANAGER_H

// 引入 Native XComponent 相关头文件，用于与 ArkUI 框架进行交互
#include <ace/xcomponent/native_interface_xcomponent.h>
// 引入标准整型定义
#include <cstdint>
// 引入 NAPI 相关的头文件，用于实现 JavaScript 和 C++ 之间的互操作
#include <js_native_api.h>
#include <js_native_api_types.h>
#include <napi/native_api.h>
// 引入字符串操作
#include <string>
// 引入哈希表，用于存储映射关系
#include <unordered_map>
// 引入 EGLCore 头文件，用于处理 EGL/OpenGL ES 相关渲染上下文
#include <surface/Surface_Core.h>
// 引入 ScintillaHarmony 头文件，用于文本编辑器功能
#include "../include/scintilla_harmony.h"

/**
 * @class PluginManager
 * @brief 负责管理 Native XComponent 的生命周期、事件分发以及原生节点创建和渲染。
 *        它作为 NAPI 接口的入口，提供给 JS 层调用，以实现 C++ 侧的渲染和交互逻辑。
 */
class PluginManager {
public:
    // OH_NativeXComponent_Callback 结构体，包含 XComponent 的生命周期回调函数指针
    static OH_NativeXComponent_Callback callback_;
    /**
     * @brief 构造函数，初始化 EGLCore 并设置 XComponent 的回调函数。
     */
    PluginManager();
    /**
     * @brief 析构函数，清理资源，包括 EGLCore 和存储的 XComponent 实例。
     */
    ~PluginManager();
    /**
     * @brief 获取 PluginManager 的单例。
     * @return PluginManager 实例的指针。
     */
    static PluginManager* GetInstance()
    {
        return &PluginManager::pluginManager_;
    }
    
    /**
     * @brief NAPI 接口：创建原生节点。
     *        JS 层调用此函数来创建基于 ArkUI Native Node 的 UI 组件，并设置其用户数据。
     * @param env NAPI 环境。
     * @param info NAPI 回调信息，包含调用参数。
     * @return 返回 nullptr。
     */
    static napi_value createNativeNode(napi_env env, napi_callback_info info);
    /**
     * @brief NAPI 接口：获取 XComponent 渲染状态。
     *        JS 层调用此函数来查询 XComponent 是否已绘制和是否已改变颜色。
     * @param env NAPI 环境。
     * @param info NAPI 回调信息。
     * @return 返回一个包含 hasDraw 和 hasChangeColor 状态的对象。
     */
    static napi_value GetXComponentStatus(napi_env env, napi_callback_info info);
    /**
     * @brief NAPI 接口：触发绘制图案。
     *        JS 层调用此函数以触发 EGLCore 进行绘制操作。
     * @param env NAPI 环境。
     * @param info NAPI 回调信息。
     * @return 返回 nullptr。
     */
    static napi_value NapiDrawPattern(napi_env env, napi_callback_info info);
    
    /**
     * @brief Surface 文本读取。
     * @param component env NAPI 环境。
     * @param info NAPI 回调信息。
     */
    static napi_value SetText(napi_env env, napi_callback_info info);

    /**
     * @brief 创建Scintilla编辑器实例。
     * @param env NAPI 环境。
     * @param info NAPI 回调信息。
     * @return 返回编辑器ID。
     */
    static napi_value CreateScintillaEditor(napi_env env, napi_callback_info info);

    /**
     * @brief 初始化Scintilla编辑器与XComponent。
     * @param env NAPI 环境。
     * @param info NAPI 回调信息。
     * @return 返回 nullptr。
     */
    static napi_value InitScintillaEditor(napi_env env, napi_callback_info info);

    /**
     * @brief 销毁Scintilla编辑器实例。
     * @param env NAPI 环境。
     * @param info NAPI 回调信息。
     * @return 返回 nullptr。
     */
    static napi_value DestroyScintillaEditor(napi_env env, napi_callback_info info);
    
    // CApi XComponent 回调函数，由 ArkUI 框架在特定事件发生时调用
    /**
     * @brief Surface 尺寸改变回调。
     * @param component OH_NativeXComponent 实例指针。
     * @param window 窗口句柄。
     */
    void OnSurfaceChanged(OH_NativeXComponent* component, void* window);
    /**
     * @brief Surface 销毁回调。
     * @param component OH_NativeXComponent 实例指针。
     * @param window 窗口句柄。
     */
    void OnSurfaceDestroyed(OH_NativeXComponent* component, void* window);
    /**
     * @brief 触摸事件分发回调。
     * @param component OH_NativeXComponent 实例指针。
     * @param window 窗口句柄。
     */
    void DispatchTouchEvent(OH_NativeXComponent* component, void* window);
    /**
     * @brief Surface 创建回调。
     * @param component OH_NativeXComponent 实例指针。
     * @param window 窗口句柄。
     */
    void OnSurfaceCreated(OH_NativeXComponent* component, void* window);
    
    

private:
    // PluginManager 的单例实例。
    static PluginManager pluginManager_;
    // 存储 XComponent ID 到 OH_NativeXComponent 指针的映射。
    std::unordered_map<std::string, OH_NativeXComponent*> nativeXComponentMap_;
    // 存储 PluginManager 实例的映射（可能用于多实例管理，但当前示例为单例模式）。
    std::unordered_map<std::string, PluginManager*> pluginManagerMap_;
    // 存储编辑器ID到ScintillaHarmony实例的映射
    static std::unordered_map<int64_t, Scintilla::Internal::ScintillaHarmony*> editorMap_;
    // 下一个编辑器ID
    static int64_t nextEditorId_;
    
public:
    // EGLCore 实例指针，用于管理 EGL 上下文和渲染。
    Surface_Core *suerfacecore_;
    // XComponent 的宽度。
    uint64_t width_;
    // XComponent 的高度。
    uint64_t height_;
    // 触摸事件信息。
    OH_NativeXComponent_TouchEvent touchEvent_;
    // 标记是否已进行绘制操作。
    static int32_t hasDraw_;
    // 标记是否已进行颜色改变操作。
    static int32_t hasChangeColor_;
};
#endif // NATIVE_XCOMPONENT_PLUGIN_MANAGER_H
