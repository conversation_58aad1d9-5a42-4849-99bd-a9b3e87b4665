// Scintilla source code edit control
/** @file LexerSimple.cxx
 ** A simple lexer with no state.
 **/
// Copyright 1998-2010 by <PERSON> <<EMAIL>>
// The License.txt file describes the conditions under which this software may be distributed.


#include "LexerSimple.h"

using namespace Lexilla;

LexerSimple::LexerSimple(const LexerModule *module_) :
	LexerBase(module_->LexClasses(), module_->NamedStyles()),
	lexerModule(module_) {
	for (int wl = 0; wl < lexerModule->GetNumWordLists(); wl++) {
		if (!wordLists.empty())
			wordLists += "\n";
		wordLists += lexerModule->GetWordListDescription(wl);
	}
}

const char * SCI_METHOD LexerSimple::DescribeWordListSets() {
	return wordLists.c_str();
}

void SCI_METHOD LexerSimple::Lex(Sci_PositionU startPos, Sci_Position lengthDoc, int initStyle, Scintilla::IDocument *pAccess) {
	Accessor astyler(pAccess, &props);
	lexerModule-><PERSON>(startPos, lengthDoc, initStyle, keyWordLists, astyler);
	astyler.Flush();
}

void SCI_METHOD LexerSimple::Fold(Sci_PositionU startPos, Sci_Position lengthDoc, int initStyle, Scintilla::IDocument *pAccess) {
	if (props.GetInt("fold")) {
		Accessor astyler(pAccess, &props);
		lexerModule->Fold(startPos, lengthDoc, initStyle, keyWordLists, astyler);
		astyler.Flush();
	}
}

const char * SCI_METHOD LexerSimple::GetName() {
	return lexerModule->languageName;
}

int SCI_METHOD LexerSimple::GetIdentifier() {
	return lexerModule->GetLanguage();
}
