## First line may be used for shbang

## This file defines the interface to Scintilla

## Copyright 2000-2003 by <PERSON> <<EMAIL>>
## The License.txt file describes the conditions under which this software may be distributed.

## A line starting with ## is a pure comment and should be stripped by readers.
## A line starting with #! is for future shbang use
## A line starting with # followed by a space is a documentation comment and refers
## to the next feature definition.

## Each feature is defined by a line starting with fun, get, set, val, evt, enu, lex, or ali.
##     cat -> start a category
##     fun -> a function
##     get -> a property get function
##     set -> a property set function
##     val -> definition of a constant
##     evt -> an event
##     enu -> associate an enumeration with a set of vals with a prefix
##     lex -> associate a lexer with the lexical classes it produces
##     ali -> add an alias for a val, commonly adding '_' to separate words
##
## All other feature names should be ignored. They may be defined in the future.
## A property may have a set function, a get function or both. Each will have
## "Get" or "Set" in their names and the corresponding name will have the obvious switch.
## A property may be subscripted, in which case the first parameter is the subscript.
## fun, get, and set features have a strict syntax:
## <featureType><ws><returnType><ws><name>[=<number](<param>,<param>)
## where <ws> stands for white space.
## param may be empty (null value) or is <paramType><ws><paramName>[=<value>]
## Additional white space is allowed between elements.
## The syntax for evt is <featureType><ws><returnType><ws><name>[=<number]([<param>[,<param>]*])
## Feature names that contain an underscore are defined by Windows, so in these
## cases, using the Windows definition is preferred where available.
## The feature numbers are stable so features will not be renumbered.
## Features may be removed but they will go through a period of deprecation
## before removal which is signalled by moving them into the Deprecated category.
##
## enu has the syntax enu<ws><enumeration>=<prefix>[<ws><prefix>]* where all the val
## features in this file starting with a given <prefix> are considered part of the
## enumeration.
##
## lex has the syntax lex<ws><name>=<lexerVal><ws><prefix>[<ws><prefix>]*
## where name is a reasonably capitalised (Python, XML) identifier or UI name,
## lexerVal is the val used to specify the lexer, and the list of prefixes is similar
## to enu. The name may not be the same as that used within the lexer so the lexerVal
## should be used to tie these entities together.

## Types: Never start with a capital letter
##     void
##     int
##     bool -> integer, 1=true, 0=false
##     position -> intptr_t position in a document
##     line -> intptr_t line in a document
##     colour -> colour integer containing red, green, and blue bytes with red as least-significant and blue as most.
##     colouralpha -> colour integer containing red, green, blue, and alpha bytes with red as least-significant and alpha as most.
##     string -> pointer to const character
##     stringresult -> pointer to character, NULL-> return size of result
##     cells -> pointer to array of cells, each cell containing a style byte and character byte
##     pointer -> void* pointer that may point to a document, loader, internal text storage or similar
##     textrange -> range of a min and a max position with an output string
##     textrangefull -> range of a min and a max position with an output string - supports 64-bit
##     findtext -> searchrange, text -> foundposition
##     findtextfull -> searchrange, text -> foundposition
##     keymod -> integer containing key in low half and modifiers in high half
##     formatrange
##     formatrangefull
## Enumeration types always start with a capital letter
## Types no longer used:
##     findtextex -> searchrange
##     charrange -> range of a min and a max position
##     charrangeresult -> like charrange, but output param
##     countedstring
##     point -> x,y
##     pointresult  -> like point, but output param
##     rectangle -> left,top,right,bottom
## Client code should ignore definitions containing types it does not understand, except
## for possibly #defining the constants

## Line numbers and positions start at 0.
## String arguments may contain NUL ('\0') characters where the calls provide a length
## argument and retrieve NUL characters. APIs marked as NUL-terminated also have a
## NUL appended but client code should calculate the size that will be returned rather
## than relying upon the NUL whenever possible. Allow for the extra NUL character when
## allocating buffers. The size to allocate for a stringresult (not including NUL) can be
## determined by calling with a NULL (0) pointer.

cat Basics

################################################
## For Scintilla.h
val INVALID_POSITION=-1
# Define start of Scintilla messages to be greater than all Windows edit (EM_*) messages
# as many EM_ messages can be used although that use is deprecated.
val SCI_START=2000
val SCI_OPTIONAL_START=3000
val SCI_LEXER_START=4000

# Add text to the document at current position.
fun void AddText=2001(position length, string text)

# Add array of cells to document.
fun void AddStyledText=2002(position length, cells c)

# Insert string at a position.
fun void InsertText=2003(position pos, string text)

# Change the text that is being inserted in response to SC_MOD_INSERTCHECK
fun void ChangeInsertion=2672(position length, string text)

# Delete all text in the document.
fun void ClearAll=2004(,)

# Delete a range of text in the document.
fun void DeleteRange=2645(position start, position lengthDelete)

# Set all style bytes to 0, remove all folding information.
fun void ClearDocumentStyle=2005(,)

# Returns the number of bytes in the document.
get position GetLength=2006(,)

# Returns the character byte at the position.
get int GetCharAt=2007(position pos,)

# Returns the position of the caret.
get position GetCurrentPos=2008(,)

# Returns the position of the opposite end of the selection to the caret.
get position GetAnchor=2009(,)

# Returns the style byte at the position.
get int GetStyleAt=2010(position pos,)

# Returns the unsigned style byte at the position.
get int GetStyleIndexAt=2038(position pos,)

# Redoes the next action on the undo history.
fun void Redo=2011(,)

# Choose between collecting actions into the undo
# history and discarding them.
set void SetUndoCollection=2012(bool collectUndo,)

# Select all the text in the document.
fun void SelectAll=2013(,)

# Remember the current position in the undo history as the position
# at which the document was saved.
fun void SetSavePoint=2014(,)

# Retrieve a buffer of cells.
# Returns the number of bytes in the buffer not including terminating NULs.
fun position GetStyledText=2015(, textrange tr)

# Retrieve a buffer of cells that can be past 2GB.
# Returns the number of bytes in the buffer not including terminating NULs.
fun position GetStyledTextFull=2778(, textrangefull tr)

# Are there any redoable actions in the undo history?
fun bool CanRedo=2016(,)

# Retrieve the line number at which a particular marker is located.
fun line MarkerLineFromHandle=2017(int markerHandle,)

# Delete a marker.
fun void MarkerDeleteHandle=2018(int markerHandle,)

# Retrieve marker handles of a line
fun int MarkerHandleFromLine=2732(line line, int which)

# Retrieve marker number of a marker handle
fun int MarkerNumberFromLine=2733(line line, int which)

# Is undo history being collected?
get bool GetUndoCollection=2019(,)

enu WhiteSpace=SCWS_
val SCWS_INVISIBLE=0
val SCWS_VISIBLEALWAYS=1
val SCWS_VISIBLEAFTERINDENT=2
val SCWS_VISIBLEONLYININDENT=3

ali SCWS_VISIBLEALWAYS=VISIBLE_ALWAYS
ali SCWS_VISIBLEAFTERINDENT=VISIBLE_AFTER_INDENT
ali SCWS_VISIBLEONLYININDENT=VISIBLE_ONLY_IN_INDENT

# Are white space characters currently visible?
# Returns one of SCWS_* constants.
get WhiteSpace GetViewWS=2020(,)

# Make white space characters invisible, always visible or visible outside indentation.
set void SetViewWS=2021(WhiteSpace viewWS,)

enu TabDrawMode=SCTD_
val SCTD_LONGARROW=0
val SCTD_STRIKEOUT=1

ali SCTD_LONGARROW=LONG_ARROW
ali SCTD_STRIKEOUT=STRIKE_OUT

# Retrieve the current tab draw mode.
# Returns one of SCTD_* constants.
get TabDrawMode GetTabDrawMode=2698(,)

# Set how tabs are drawn when visible.
set void SetTabDrawMode=2699(TabDrawMode tabDrawMode,)

# Find the position from a point within the window.
fun position PositionFromPoint=2022(int x, int y)

# Find the position from a point within the window but return
# INVALID_POSITION if not close to text.
fun position PositionFromPointClose=2023(int x, int y)

# Set caret to start of a line and ensure it is visible.
fun void GotoLine=2024(line line,)

# Set caret to a position and ensure it is visible.
fun void GotoPos=2025(position caret,)

# Set the selection anchor to a position. The anchor is the opposite
# end of the selection from the caret.
set void SetAnchor=2026(position anchor,)

# Retrieve the text of the line containing the caret.
# Returns the index of the caret on the line.
# Result is NUL-terminated.
fun position GetCurLine=2027(position length, stringresult text)

# Retrieve the position of the last correctly styled character.
get position GetEndStyled=2028(,)

enu EndOfLine=SC_EOL_
val SC_EOL_CRLF=0
val SC_EOL_CR=1
val SC_EOL_LF=2

ali SC_EOL_CRLF=CR_LF

# Convert all line endings in the document to one mode.
fun void ConvertEOLs=2029(EndOfLine eolMode,)

# Retrieve the current end of line mode - one of CRLF, CR, or LF.
get EndOfLine GetEOLMode=2030(,)

# Set the current end of line mode.
set void SetEOLMode=2031(EndOfLine eolMode,)

# Set the current styling position to start.
# The unused parameter is no longer used and should be set to 0.
fun void StartStyling=2032(position start, int unused)

# Change style from current styling position for length characters to a style
# and move the current styling position to after this newly styled segment.
fun void SetStyling=2033(position length, int style)

# Is drawing done first into a buffer or direct to the screen?
get bool GetBufferedDraw=2034(,)

# If drawing is buffered then each line of text is drawn into a bitmap buffer
# before drawing it to the screen to avoid flicker.
set void SetBufferedDraw=2035(bool buffered,)

# Change the visible size of a tab to be a multiple of the width of a space character.
set void SetTabWidth=2036(int tabWidth,)

# Retrieve the visible size of a tab.
get int GetTabWidth=2121(,)

# Set the minimum visual width of a tab.
set void SetTabMinimumWidth=2724(int pixels,)

# Get the minimum visual width of a tab.
get int GetTabMinimumWidth=2725(,)

# Clear explicit tabstops on a line.
fun void ClearTabStops=2675(line line,)

# Add an explicit tab stop for a line.
fun void AddTabStop=2676(line line, int x)

# Find the next explicit tab stop position on a line after a position.
fun int GetNextTabStop=2677(line line, int x)

# The SC_CP_UTF8 value can be used to enter Unicode mode.
# This is the same value as CP_UTF8 in Windows
val SC_CP_UTF8=65001

# Set the code page used to interpret the bytes of the document as characters.
# The SC_CP_UTF8 value can be used to enter Unicode mode.
set void SetCodePage=2037(int codePage,)

# Set the locale for displaying text.
set void SetFontLocale=2760(, string localeName)

# Get the locale for displaying text.
get int GetFontLocale=2761(, stringresult localeName)

enu IMEInteraction=SC_IME_
val SC_IME_WINDOWED=0
val SC_IME_INLINE=1

# Is the IME displayed in a window or inline?
get IMEInteraction GetIMEInteraction=2678(,)

# Choose to display the IME in a window or inline.
set void SetIMEInteraction=2679(IMEInteraction imeInteraction,)

enu Alpha=SC_ALPHA_
val SC_ALPHA_TRANSPARENT=0
val SC_ALPHA_OPAQUE=255
val SC_ALPHA_NOALPHA=256

ali SC_ALPHA_NOALPHA=NO_ALPHA

enu CursorShape=SC_CURSOR
val SC_CURSORNORMAL=-1
val SC_CURSORARROW=2
val SC_CURSORWAIT=4
val SC_CURSORREVERSEARROW=7

ali SC_CURSORREVERSEARROW=REVERSE_ARROW

enu MarkerSymbol=SC_MARK_
val MARKER_MAX=31
val SC_MARK_CIRCLE=0
val SC_MARK_ROUNDRECT=1
val SC_MARK_ARROW=2
val SC_MARK_SMALLRECT=3
val SC_MARK_SHORTARROW=4
val SC_MARK_EMPTY=5
val SC_MARK_ARROWDOWN=6
val SC_MARK_MINUS=7
val SC_MARK_PLUS=8

# Shapes used for outlining column.
val SC_MARK_VLINE=9
val SC_MARK_LCORNER=10
val SC_MARK_TCORNER=11
val SC_MARK_BOXPLUS=12
val SC_MARK_BOXPLUSCONNECTED=13
val SC_MARK_BOXMINUS=14
val SC_MARK_BOXMINUSCONNECTED=15
val SC_MARK_LCORNERCURVE=16
val SC_MARK_TCORNERCURVE=17
val SC_MARK_CIRCLEPLUS=18
val SC_MARK_CIRCLEPLUSCONNECTED=19
val SC_MARK_CIRCLEMINUS=20
val SC_MARK_CIRCLEMINUSCONNECTED=21

# Invisible mark that only sets the line background colour.
val SC_MARK_BACKGROUND=22
val SC_MARK_DOTDOTDOT=23
val SC_MARK_ARROWS=24
val SC_MARK_PIXMAP=25
val SC_MARK_FULLRECT=26
val SC_MARK_LEFTRECT=27
val SC_MARK_AVAILABLE=28
val SC_MARK_UNDERLINE=29
val SC_MARK_RGBAIMAGE=30
val SC_MARK_BOOKMARK=31
val SC_MARK_VERTICALBOOKMARK=32
val SC_MARK_BAR=33

val SC_MARK_CHARACTER=10000

ali SC_MARK_ROUNDRECT=ROUND_RECT
ali SC_MARK_SMALLRECT=SMALL_RECT
ali SC_MARK_SHORTARROW=SHORT_ARROW
ali SC_MARK_ARROWDOWN=ARROW_DOWN
ali SC_MARK_VLINE=V_LINE
ali SC_MARK_LCORNER=L_CORNER
ali SC_MARK_TCORNER=T_CORNER
ali SC_MARK_BOXPLUS=BOX_PLUS
ali SC_MARK_BOXPLUSCONNECTED=BOX_PLUS_CONNECTED
ali SC_MARK_BOXMINUS=BOX_MINUS
ali SC_MARK_BOXMINUSCONNECTED=BOX_MINUS_CONNECTED
ali SC_MARK_LCORNERCURVE=L_CORNER_CURVE
ali SC_MARK_TCORNERCURVE=T_CORNER_CURVE
ali SC_MARK_CIRCLEPLUS=CIRCLE_PLUS
ali SC_MARK_CIRCLEPLUSCONNECTED=CIRCLE_PLUS_CONNECTED
ali SC_MARK_CIRCLEMINUS=CIRCLE_MINUS
ali SC_MARK_CIRCLEMINUSCONNECTED=CIRCLE_MINUS_CONNECTED
ali SC_MARK_DOTDOTDOT=DOT_DOT_DOT
ali SC_MARK_FULLRECT=FULL_RECT
ali SC_MARK_LEFTRECT=LEFT_RECT
ali SC_MARK_RGBAIMAGE=RGBA_IMAGE
ali SC_MARK_VERTICALBOOKMARK=VERTICAL_BOOKMARK

enu MarkerOutline=SC_MARKNUM_
# Markers used for outlining and change history columns.
val SC_MARKNUM_HISTORY_REVERTED_TO_ORIGIN=21
val SC_MARKNUM_HISTORY_SAVED=22
val SC_MARKNUM_HISTORY_MODIFIED=23
val SC_MARKNUM_HISTORY_REVERTED_TO_MODIFIED=24
val SC_MARKNUM_FOLDEREND=25
val SC_MARKNUM_FOLDEROPENMID=26
val SC_MARKNUM_FOLDERMIDTAIL=27
val SC_MARKNUM_FOLDERTAIL=28
val SC_MARKNUM_FOLDERSUB=29
val SC_MARKNUM_FOLDER=30
val SC_MARKNUM_FOLDEROPEN=31

ali SC_MARKNUM_FOLDEREND=FOLDER_END
ali SC_MARKNUM_FOLDEROPENMID=FOLDER_OPEN_MID
ali SC_MARKNUM_FOLDERMIDTAIL=FOLDER_MID_TAIL
ali SC_MARKNUM_FOLDERTAIL=FOLDER_TAIL
ali SC_MARKNUM_FOLDERSUB=FOLDER_SUB
ali SC_MARKNUM_FOLDEROPEN=FOLDER_OPEN

val SC_MASK_HISTORY=0x01E00000

# SC_MASK_FOLDERS doesn't go in an enumeration as larger than max 32-bit positive integer
val SC_MASK_FOLDERS=0xFE000000

# Set the symbol used for a particular marker number.
fun void MarkerDefine=2040(int markerNumber, MarkerSymbol markerSymbol)

# Set the foreground colour used for a particular marker number.
set void MarkerSetFore=2041(int markerNumber, colour fore)

# Set the background colour used for a particular marker number.
set void MarkerSetBack=2042(int markerNumber, colour back)

# Set the background colour used for a particular marker number when its folding block is selected.
set void MarkerSetBackSelected=2292(int markerNumber, colour back)

# Set the foreground colour used for a particular marker number.
set void MarkerSetForeTranslucent=2294(int markerNumber, colouralpha fore)

# Set the background colour used for a particular marker number.
set void MarkerSetBackTranslucent=2295(int markerNumber, colouralpha back)

# Set the background colour used for a particular marker number when its folding block is selected.
set void MarkerSetBackSelectedTranslucent=2296(int markerNumber, colouralpha back)

# Set the width of strokes used in .01 pixels so 50  = 1/2 pixel width.
set void MarkerSetStrokeWidth=2297(int markerNumber, int hundredths)

# Enable/disable highlight for current folding block (smallest one that contains the caret)
fun void MarkerEnableHighlight=2293(bool enabled,)

# Add a marker to a line, returning an ID which can be used to find or delete the marker.
fun int MarkerAdd=2043(line line, int markerNumber)

# Delete a marker from a line.
fun void MarkerDelete=2044(line line, int markerNumber)

# Delete all markers with a particular number from all lines.
fun void MarkerDeleteAll=2045(int markerNumber,)

# Get a bit mask of all the markers set on a line.
fun int MarkerGet=2046(line line,)

# Find the next line at or after lineStart that includes a marker in mask.
# Return -1 when no more lines.
fun line MarkerNext=2047(line lineStart, int markerMask)

# Find the previous line before lineStart that includes a marker in mask.
fun line MarkerPrevious=2048(line lineStart, int markerMask)

# Define a marker from a pixmap.
fun void MarkerDefinePixmap=2049(int markerNumber, string pixmap)

# Add a set of markers to a line.
fun void MarkerAddSet=2466(line line, int markerSet)

# Set the alpha used for a marker that is drawn in the text area, not the margin.
set void MarkerSetAlpha=2476(int markerNumber, Alpha alpha)

# Get the layer used for a marker that is drawn in the text area, not the margin.
get Layer MarkerGetLayer=2734(int markerNumber,)

# Set the layer used for a marker that is drawn in the text area, not the margin.
set void MarkerSetLayer=2735(int markerNumber, Layer layer)

val SC_MAX_MARGIN=4

enu MarginType=SC_MARGIN_
val SC_MARGIN_SYMBOL=0
val SC_MARGIN_NUMBER=1
val SC_MARGIN_BACK=2
val SC_MARGIN_FORE=3
val SC_MARGIN_TEXT=4
val SC_MARGIN_RTEXT=5
val SC_MARGIN_COLOUR=6

ali SC_MARGIN_RTEXT=R_TEXT

# Set a margin to be either numeric or symbolic.
set void SetMarginTypeN=2240(int margin, MarginType marginType)

# Retrieve the type of a margin.
get MarginType GetMarginTypeN=2241(int margin,)

# Set the width of a margin to a width expressed in pixels.
set void SetMarginWidthN=2242(int margin, int pixelWidth)

# Retrieve the width of a margin in pixels.
get int GetMarginWidthN=2243(int margin,)

# Set a mask that determines which markers are displayed in a margin.
set void SetMarginMaskN=2244(int margin, int mask)

# Retrieve the marker mask of a margin.
get int GetMarginMaskN=2245(int margin,)

# Make a margin sensitive or insensitive to mouse clicks.
set void SetMarginSensitiveN=2246(int margin, bool sensitive)

# Retrieve the mouse click sensitivity of a margin.
get bool GetMarginSensitiveN=2247(int margin,)

# Set the cursor shown when the mouse is inside a margin.
set void SetMarginCursorN=2248(int margin, CursorShape cursor)

# Retrieve the cursor shown in a margin.
get CursorShape GetMarginCursorN=2249(int margin,)

# Set the background colour of a margin. Only visible for SC_MARGIN_COLOUR.
set void SetMarginBackN=2250(int margin, colour back)

# Retrieve the background colour of a margin
get colour GetMarginBackN=2251(int margin,)

# Allocate a non-standard number of margins.
set void SetMargins=2252(int margins,)

# How many margins are there?.
get int GetMargins=2253(,)

# Styles in range 32..39 are predefined for parts of the UI and are not used as normal styles.
enu StylesCommon=STYLE_
val STYLE_DEFAULT=32
val STYLE_LINENUMBER=33
val STYLE_BRACELIGHT=34
val STYLE_BRACEBAD=35
val STYLE_CONTROLCHAR=36
val STYLE_INDENTGUIDE=37
val STYLE_CALLTIP=38
val STYLE_FOLDDISPLAYTEXT=39
val STYLE_LASTPREDEFINED=39
val STYLE_MAX=255

ali STYLE_LINENUMBER=LINE_NUMBER
ali STYLE_BRACELIGHT=BRACE_LIGHT
ali STYLE_BRACEBAD=BRACE_BAD
ali STYLE_CONTROLCHAR=CONTROL_CHAR
ali STYLE_INDENTGUIDE=INDENT_GUIDE
ali STYLE_CALLTIP=CALL_TIP
ali STYLE_FOLDDISPLAYTEXT=FOLD_DISPLAY_TEXT
ali STYLE_LASTPREDEFINED=LAST_PREDEFINED

# Character set identifiers are used in StyleSetCharacterSet.
# The values are the same as the Windows *_CHARSET values.
enu CharacterSet=SC_CHARSET_
val SC_CHARSET_ANSI=0
val SC_CHARSET_DEFAULT=1
val SC_CHARSET_BALTIC=186
val SC_CHARSET_CHINESEBIG5=136
val SC_CHARSET_EASTEUROPE=238
val SC_CHARSET_GB2312=134
val SC_CHARSET_GREEK=161
val SC_CHARSET_HANGUL=129
val SC_CHARSET_MAC=77
val SC_CHARSET_OEM=255
val SC_CHARSET_RUSSIAN=204
val SC_CHARSET_OEM866=866
val SC_CHARSET_CYRILLIC=1251
val SC_CHARSET_SHIFTJIS=128
val SC_CHARSET_SYMBOL=2
val SC_CHARSET_TURKISH=162
val SC_CHARSET_JOHAB=130
val SC_CHARSET_HEBREW=177
val SC_CHARSET_ARABIC=178
val SC_CHARSET_VIETNAMESE=163
val SC_CHARSET_THAI=222
val SC_CHARSET_8859_15=1000

ali SC_CHARSET_CHINESEBIG5=CHINESE_BIG5
ali SC_CHARSET_EASTEUROPE=EAST_EUROPE
ali SC_CHARSET_GB2312=G_B_2312
ali SC_CHARSET_OEM866=OEM_866
ali SC_CHARSET_SHIFTJIS=SHIFT_JIS
ali SC_CHARSET_8859_15=ISO_8859_15

# Clear all the styles and make equivalent to the global default style.
fun void StyleClearAll=2050(,)

# Set the foreground colour of a style.
set void StyleSetFore=2051(int style, colour fore)

# Set the background colour of a style.
set void StyleSetBack=2052(int style, colour back)

# Set a style to be bold or not.
set void StyleSetBold=2053(int style, bool bold)

# Set a style to be italic or not.
set void StyleSetItalic=2054(int style, bool italic)

# Set the size of characters of a style.
set void StyleSetSize=2055(int style, int sizePoints)

# Set the font of a style.
set void StyleSetFont=2056(int style, string fontName)

# Set a style to have its end of line filled or not.
set void StyleSetEOLFilled=2057(int style, bool eolFilled)

# Reset the default style to its state at startup
fun void StyleResetDefault=2058(,)

# Set a style to be underlined or not.
set void StyleSetUnderline=2059(int style, bool underline)

enu CaseVisible=SC_CASE_
val SC_CASE_MIXED=0
val SC_CASE_UPPER=1
val SC_CASE_LOWER=2
val SC_CASE_CAMEL=3

# Get the foreground colour of a style.
get colour StyleGetFore=2481(int style,)

# Get the background colour of a style.
get colour StyleGetBack=2482(int style,)

# Get is a style bold or not.
get bool StyleGetBold=2483(int style,)

# Get is a style italic or not.
get bool StyleGetItalic=2484(int style,)

# Get the size of characters of a style.
get int StyleGetSize=2485(int style,)

# Get the font of a style.
# Returns the length of the fontName
# Result is NUL-terminated.
get int StyleGetFont=2486(int style, stringresult fontName)

# Get is a style to have its end of line filled or not.
get bool StyleGetEOLFilled=2487(int style,)

# Get is a style underlined or not.
get bool StyleGetUnderline=2488(int style,)

# Get is a style mixed case, or to force upper or lower case.
get CaseVisible StyleGetCase=2489(int style,)

# Get the character get of the font in a style.
get CharacterSet StyleGetCharacterSet=2490(int style,)

# Get is a style visible or not.
get bool StyleGetVisible=2491(int style,)

# Get is a style changeable or not (read only).
# Experimental feature, currently buggy.
get bool StyleGetChangeable=2492(int style,)

# Get is a style a hotspot or not.
get bool StyleGetHotSpot=2493(int style,)

# Set a style to be mixed case, or to force upper or lower case.
set void StyleSetCase=2060(int style, CaseVisible caseVisible)

val SC_FONT_SIZE_MULTIPLIER=100

# Set the size of characters of a style. Size is in points multiplied by 100.
set void StyleSetSizeFractional=2061(int style, int sizeHundredthPoints)

# Get the size of characters of a style in points multiplied by 100
get int StyleGetSizeFractional=2062(int style,)

enu FontWeight=SC_WEIGHT_
val SC_WEIGHT_NORMAL=400
val SC_WEIGHT_SEMIBOLD=600
val SC_WEIGHT_BOLD=700

ali SC_WEIGHT_SEMIBOLD=SEMI_BOLD

# Set the weight of characters of a style.
set void StyleSetWeight=2063(int style, FontWeight weight)

# Get the weight of characters of a style.
get FontWeight StyleGetWeight=2064(int style,)

# Set the character set of the font in a style.
set void StyleSetCharacterSet=2066(int style, CharacterSet characterSet)

# Set a style to be a hotspot or not.
set void StyleSetHotSpot=2409(int style, bool hotspot)

# Indicate that a style may be monospaced over ASCII graphics characters which enables optimizations.
set void StyleSetCheckMonospaced=2254(int style, bool checkMonospaced)

# Get whether a style may be monospaced.
get bool StyleGetCheckMonospaced=2255(int style,)

enu FontStretch=SC_STRETCH_
val SC_STRETCH_ULTRA_CONDENSED=1
val SC_STRETCH_EXTRA_CONDENSED=2
val SC_STRETCH_CONDENSED=3
val SC_STRETCH_SEMI_CONDENSED=4
val SC_STRETCH_NORMAL=5
val SC_STRETCH_SEMI_EXPANDED=6
val SC_STRETCH_EXPANDED=7
val SC_STRETCH_EXTRA_EXPANDED=8
val SC_STRETCH_ULTRA_EXPANDED=9

# Set the stretch of characters of a style.
set void StyleSetStretch=2258(int style, FontStretch stretch)

# Get the stretch of characters of a style.
get FontStretch StyleGetStretch=2259(int style,)

# Set the invisible representation for a style.
set void StyleSetInvisibleRepresentation=2256(int style, string representation)

# Get the invisible representation for a style.
get int StyleGetInvisibleRepresentation=2257(int style, stringresult representation)

enu Element=SC_ELEMENT_
val SC_ELEMENT_LIST=0
val SC_ELEMENT_LIST_BACK=1
val SC_ELEMENT_LIST_SELECTED=2
val SC_ELEMENT_LIST_SELECTED_BACK=3
val SC_ELEMENT_SELECTION_TEXT=10
val SC_ELEMENT_SELECTION_BACK=11
val SC_ELEMENT_SELECTION_ADDITIONAL_TEXT=12
val SC_ELEMENT_SELECTION_ADDITIONAL_BACK=13
val SC_ELEMENT_SELECTION_SECONDARY_TEXT=14
val SC_ELEMENT_SELECTION_SECONDARY_BACK=15
val SC_ELEMENT_SELECTION_INACTIVE_TEXT=16
val SC_ELEMENT_SELECTION_INACTIVE_BACK=17
val SC_ELEMENT_SELECTION_INACTIVE_ADDITIONAL_TEXT=18
val SC_ELEMENT_SELECTION_INACTIVE_ADDITIONAL_BACK=19
val SC_ELEMENT_CARET=40
val SC_ELEMENT_CARET_ADDITIONAL=41
val SC_ELEMENT_CARET_LINE_BACK=50
val SC_ELEMENT_WHITE_SPACE=60
val SC_ELEMENT_WHITE_SPACE_BACK=61
val SC_ELEMENT_HOT_SPOT_ACTIVE=70
val SC_ELEMENT_HOT_SPOT_ACTIVE_BACK=71
val SC_ELEMENT_FOLD_LINE=80
val SC_ELEMENT_HIDDEN_LINE=81

# Set the colour of an element. Translucency (alpha) may or may not be significant
# and this may depend on the platform. The alpha byte should commonly be 0xff for opaque.
set void SetElementColour=2753(Element element, colouralpha colourElement)

# Get the colour of an element.
get colouralpha GetElementColour=2754(Element element,)

# Use the default or platform-defined colour for an element.
fun void ResetElementColour=2755(Element element,)

# Get whether an element has been set by SetElementColour.
# When false, a platform-defined or default colour is used.
get bool GetElementIsSet=2756(Element element,)

# Get whether an element supports translucency.
get bool GetElementAllowsTranslucent=2757(Element element,)

# Get the colour of an element.
get colouralpha GetElementBaseColour=2758(Element element,)

# Set the foreground colour of the main and additional selections and whether to use this setting.
fun void SetSelFore=2067(bool useSetting, colour fore)

# Set the background colour of the main and additional selections and whether to use this setting.
fun void SetSelBack=2068(bool useSetting, colour back)

# Get the alpha of the selection.
get Alpha GetSelAlpha=2477(,)

# Set the alpha of the selection.
set void SetSelAlpha=2478(Alpha alpha,)

# Is the selection end of line filled?
get bool GetSelEOLFilled=2479(,)

# Set the selection to have its end of line filled or not.
set void SetSelEOLFilled=2480(bool filled,)

enu Layer=SC_LAYER_
val SC_LAYER_BASE=0
val SC_LAYER_UNDER_TEXT=1
val SC_LAYER_OVER_TEXT=2

# Get the layer for drawing selections
get Layer GetSelectionLayer=2762(,)

# Set the layer for drawing selections: either opaquely on base layer or translucently over text
set void SetSelectionLayer=2763(Layer layer,)

# Get the layer of the background of the line containing the caret.
get Layer GetCaretLineLayer=2764(,)

# Set the layer of the background of the line containing the caret.
set void SetCaretLineLayer=2765(Layer layer,)

# Get only highlighting subline instead of whole line.
get bool GetCaretLineHighlightSubLine=2773(,)

# Set only highlighting subline instead of whole line.
set void SetCaretLineHighlightSubLine=2774(bool subLine,)

# Set the foreground colour of the caret.
set void SetCaretFore=2069(colour fore,)

# When key+modifier combination keyDefinition is pressed perform sciCommand.
fun void AssignCmdKey=2070(keymod keyDefinition, int sciCommand)

# When key+modifier combination keyDefinition is pressed do nothing.
fun void ClearCmdKey=2071(keymod keyDefinition,)

# Drop all key mappings.
fun void ClearAllCmdKeys=2072(,)

# Set the styles for a segment of the document.
fun void SetStylingEx=2073(position length, string styles)

# Set a style to be visible or not.
set void StyleSetVisible=2074(int style, bool visible)

# Get the time in milliseconds that the caret is on and off.
get int GetCaretPeriod=2075(,)

# Get the time in milliseconds that the caret is on and off. 0 = steady on.
set void SetCaretPeriod=2076(int periodMilliseconds,)

# Set the set of characters making up words for when moving or selecting by word.
# First sets defaults like SetCharsDefault.
set void SetWordChars=2077(, string characters)

# Get the set of characters making up words for when moving or selecting by word.
# Returns the number of characters
get int GetWordChars=2646(, stringresult characters)

# Set the number of characters to have directly indexed categories
set void SetCharacterCategoryOptimization=2720(int countCharacters,)

# Get the number of characters to have directly indexed categories
get int GetCharacterCategoryOptimization=2721(,)

# Start a sequence of actions that is undone and redone as a unit.
# May be nested.
fun void BeginUndoAction=2078(,)

# End a sequence of actions that is undone and redone as a unit.
fun void EndUndoAction=2079(,)

# Is an undo sequence active?
get int GetUndoSequence=2799(,)

# How many undo actions are in the history?
get int GetUndoActions=2790(,)

# Set action as the save point
set void SetUndoSavePoint=2791(int action,)

# Which action is the save point?
get int GetUndoSavePoint=2792(,)

# Set action as the detach point
set void SetUndoDetach=2793(int action,)

# Which action is the detach point?
get int GetUndoDetach=2794(,)

# Set action as the tentative point
set void SetUndoTentative=2795(int action,)

# Which action is the tentative point?
get int GetUndoTentative=2796(,)

# Set action as the current point
set void SetUndoCurrent=2797(int action,)

# Which action is the current point?
get int GetUndoCurrent=2798(,)

# Push one action onto undo history with no text
fun void PushUndoActionType=2800(int type, position pos)

# Set the text and length of the most recently pushed action
fun void ChangeLastUndoActionText=2801(position length, string text)

# What is the type of an action?
get int GetUndoActionType=2802(int action,)

# What is the position of an action?
get position GetUndoActionPosition=2803(int action,)

# What is the text of an action?
get int GetUndoActionText=2804(int action, stringresult text)

# Indicator style enumeration and some constants
enu IndicatorStyle=INDIC_
val INDIC_PLAIN=0
val INDIC_SQUIGGLE=1
val INDIC_TT=2
val INDIC_DIAGONAL=3
val INDIC_STRIKE=4
val INDIC_HIDDEN=5
val INDIC_BOX=6
val INDIC_ROUNDBOX=7
val INDIC_STRAIGHTBOX=8
val INDIC_DASH=9
val INDIC_DOTS=10
val INDIC_SQUIGGLELOW=11
val INDIC_DOTBOX=12
val INDIC_SQUIGGLEPIXMAP=13
val INDIC_COMPOSITIONTHICK=14
val INDIC_COMPOSITIONTHIN=15
val INDIC_FULLBOX=16
val INDIC_TEXTFORE=17
val INDIC_POINT=18
val INDIC_POINTCHARACTER=19
val INDIC_GRADIENT=20
val INDIC_GRADIENTCENTRE=21
val INDIC_POINT_TOP=22

# INDIC_CONTAINER, INDIC_IME, INDIC_IME_MAX, and INDIC_MAX are indicator numbers,
# not IndicatorStyles so should not really be in the INDIC_ enumeration.
# They are redeclared in IndicatorNumbers INDICATOR_.
val INDIC_CONTAINER=8
val INDIC_IME=32
val INDIC_IME_MAX=35
val INDIC_MAX=35

enu IndicatorNumbers=INDICATOR_
val INDICATOR_CONTAINER=8
val INDICATOR_IME=32
val INDICATOR_IME_MAX=35
val INDICATOR_HISTORY_REVERTED_TO_ORIGIN_INSERTION=36
val INDICATOR_HISTORY_REVERTED_TO_ORIGIN_DELETION=37
val INDICATOR_HISTORY_SAVED_INSERTION=38
val INDICATOR_HISTORY_SAVED_DELETION=39
val INDICATOR_HISTORY_MODIFIED_INSERTION=40
val INDICATOR_HISTORY_MODIFIED_DELETION=41
val INDICATOR_HISTORY_REVERTED_TO_MODIFIED_INSERTION=42
val INDICATOR_HISTORY_REVERTED_TO_MODIFIED_DELETION=43
val INDICATOR_MAX=43

ali INDIC_TT=T_T
ali INDIC_ROUNDBOX=ROUND_BOX
ali INDIC_STRAIGHTBOX=STRAIGHT_BOX
ali INDIC_SQUIGGLELOW=SQUIGGLE_LOW
ali INDIC_DOTBOX=DOT_BOX
ali INDIC_SQUIGGLEPIXMAP=SQUIGGLE_PIXMAP
ali INDIC_COMPOSITIONTHICK=COMPOSITION_THICK
ali INDIC_COMPOSITIONTHIN=COMPOSITION_THIN
ali INDIC_FULLBOX=FULL_BOX
ali INDIC_TEXTFORE=TEXT_FORE
ali INDIC_POINTCHARACTER=POINT_CHARACTER
ali INDIC_GRADIENTCENTRE=GRADIENT_CENTRE

# Set an indicator to plain, squiggle or TT.
set void IndicSetStyle=2080(int indicator, IndicatorStyle indicatorStyle)

# Retrieve the style of an indicator.
get IndicatorStyle IndicGetStyle=2081(int indicator,)

# Set the foreground colour of an indicator.
set void IndicSetFore=2082(int indicator, colour fore)

# Retrieve the foreground colour of an indicator.
get colour IndicGetFore=2083(int indicator,)

# Set an indicator to draw under text or over(default).
set void IndicSetUnder=2510(int indicator, bool under)

# Retrieve whether indicator drawn under or over text.
get bool IndicGetUnder=2511(int indicator,)

# Set a hover indicator to plain, squiggle or TT.
set void IndicSetHoverStyle=2680(int indicator, IndicatorStyle indicatorStyle)

# Retrieve the hover style of an indicator.
get IndicatorStyle IndicGetHoverStyle=2681(int indicator,)

# Set the foreground hover colour of an indicator.
set void IndicSetHoverFore=2682(int indicator, colour fore)

# Retrieve the foreground hover colour of an indicator.
get colour IndicGetHoverFore=2683(int indicator,)

enu IndicValue=SC_INDICVALUE
val SC_INDICVALUEBIT=0x1000000
val SC_INDICVALUEMASK=0xFFFFFF

enu IndicFlag=SC_INDICFLAG_
val SC_INDICFLAG_NONE=0
val SC_INDICFLAG_VALUEFORE=1

ali SC_INDICFLAG_VALUEFORE=VALUE_FORE

# Set the attributes of an indicator.
set void IndicSetFlags=2684(int indicator, IndicFlag flags)

# Retrieve the attributes of an indicator.
get IndicFlag IndicGetFlags=2685(int indicator,)

# Set the stroke width of an indicator in hundredths of a pixel.
set void IndicSetStrokeWidth=2751(int indicator, int hundredths)

# Retrieve the stroke width of an indicator.
get int IndicGetStrokeWidth=2752(int indicator,)

# Set the foreground colour of all whitespace and whether to use this setting.
fun void SetWhitespaceFore=2084(bool useSetting, colour fore)

# Set the background colour of all whitespace and whether to use this setting.
fun void SetWhitespaceBack=2085(bool useSetting, colour back)

# Set the size of the dots used to mark space characters.
set void SetWhitespaceSize=2086(int size,)

# Get the size of the dots used to mark space characters.
get int GetWhitespaceSize=2087(,)

# Used to hold extra styling information for each line.
set void SetLineState=2092(line line, int state)

# Retrieve the extra styling information for a line.
get int GetLineState=2093(line line,)

# Retrieve the last line number that has line state.
get int GetMaxLineState=2094(,)

# Is the background of the line containing the caret in a different colour?
get bool GetCaretLineVisible=2095(,)

# Display the background of the line containing the caret in a different colour.
set void SetCaretLineVisible=2096(bool show,)

# Get the colour of the background of the line containing the caret.
get colour GetCaretLineBack=2097(,)

# Set the colour of the background of the line containing the caret.
set void SetCaretLineBack=2098(colour back,)

# Retrieve the caret line frame width.
# Width = 0 means this option is disabled.
get int GetCaretLineFrame=2704(,)

# Display the caret line framed.
# Set width != 0 to enable this option and width = 0 to disable it.
set void SetCaretLineFrame=2705(int width,)

# Set a style to be changeable or not (read only).
# Experimental feature, currently buggy.
set void StyleSetChangeable=2099(int style, bool changeable)

# Display a auto-completion list.
# The lengthEntered parameter indicates how many characters before
# the caret should be used to provide context.
fun void AutoCShow=2100(position lengthEntered, string itemList)

# Remove the auto-completion list from the screen.
fun void AutoCCancel=2101(,)

# Is there an auto-completion list visible?
fun bool AutoCActive=2102(,)

# Retrieve the position of the caret when the auto-completion list was displayed.
fun position AutoCPosStart=2103(,)

# User has selected an item so remove the list and insert the selection.
fun void AutoCComplete=2104(,)

# Define a set of character that when typed cancel the auto-completion list.
fun void AutoCStops=2105(, string characterSet)

# Change the separator character in the string setting up an auto-completion list.
# Default is space but can be changed if items contain space.
set void AutoCSetSeparator=2106(int separatorCharacter,)

# Retrieve the auto-completion list separator character.
get int AutoCGetSeparator=2107(,)

# Select the item in the auto-completion list that starts with a string.
fun void AutoCSelect=2108(, string select)

# Should the auto-completion list be cancelled if the user backspaces to a
# position before where the box was created.
set void AutoCSetCancelAtStart=2110(bool cancel,)

# Retrieve whether auto-completion cancelled by backspacing before start.
get bool AutoCGetCancelAtStart=2111(,)

# Define a set of characters that when typed will cause the autocompletion to
# choose the selected item.
set void AutoCSetFillUps=2112(, string characterSet)

# Should a single item auto-completion list automatically choose the item.
set void AutoCSetChooseSingle=2113(bool chooseSingle,)

# Retrieve whether a single item auto-completion list automatically choose the item.
get bool AutoCGetChooseSingle=2114(,)

# Set whether case is significant when performing auto-completion searches.
set void AutoCSetIgnoreCase=2115(bool ignoreCase,)

# Retrieve state of ignore case flag.
get bool AutoCGetIgnoreCase=2116(,)

# Display a list of strings and send notification when user chooses one.
fun void UserListShow=2117(int listType, string itemList)

# Set whether or not autocompletion is hidden automatically when nothing matches.
set void AutoCSetAutoHide=2118(bool autoHide,)

# Retrieve whether or not autocompletion is hidden automatically when nothing matches.
get bool AutoCGetAutoHide=2119(,)

# Define option flags for autocompletion lists
enu AutoCompleteOption=SC_AUTOCOMPLETE_
val SC_AUTOCOMPLETE_NORMAL=0
# Win32 specific:
val SC_AUTOCOMPLETE_FIXED_SIZE=1
# Always select the first item in the autocompletion list:
val SC_AUTOCOMPLETE_SELECT_FIRST_ITEM=2

# Set autocompletion options.
set void AutoCSetOptions=2638(AutoCompleteOption options,)

# Retrieve autocompletion options.
get AutoCompleteOption AutoCGetOptions=2639(,)

# Set whether or not autocompletion deletes any word characters
# after the inserted text upon completion.
set void AutoCSetDropRestOfWord=2270(bool dropRestOfWord,)

# Retrieve whether or not autocompletion deletes any word characters
# after the inserted text upon completion.
get bool AutoCGetDropRestOfWord=2271(,)

# Register an XPM image for use in autocompletion lists.
fun void RegisterImage=2405(int type, string xpmData)

# Clear all the registered XPM images.
fun void ClearRegisteredImages=2408(,)

# Retrieve the auto-completion list type-separator character.
get int AutoCGetTypeSeparator=2285(,)

# Change the type-separator character in the string setting up an auto-completion list.
# Default is '?' but can be changed if items contain '?'.
set void AutoCSetTypeSeparator=2286(int separatorCharacter,)

# Set the maximum width, in characters, of auto-completion and user lists.
# Set to 0 to autosize to fit longest item, which is the default.
set void AutoCSetMaxWidth=2208(int characterCount,)

# Get the maximum width, in characters, of auto-completion and user lists.
get int AutoCGetMaxWidth=2209(,)

# Set the maximum height, in rows, of auto-completion and user lists.
# The default is 5 rows.
set void AutoCSetMaxHeight=2210(int rowCount,)

# Set the maximum height, in rows, of auto-completion and user lists.
get int AutoCGetMaxHeight=2211(,)

# Set the style number used for auto-completion and user lists fonts.
set void AutoCSetStyle=2109(int style,)

# Get the style number used for auto-completion and user lists fonts.
get int AutoCGetStyle=2120(,)

# Set the number of spaces used for one level of indentation.
set void SetIndent=2122(int indentSize,)

# Retrieve indentation size.
get int GetIndent=2123(,)

# Indentation will only use space characters if useTabs is false, otherwise
# it will use a combination of tabs and spaces.
set void SetUseTabs=2124(bool useTabs,)

# Retrieve whether tabs will be used in indentation.
get bool GetUseTabs=2125(,)

# Change the indentation of a line to a number of columns.
set void SetLineIndentation=2126(line line, int indentation)

# Retrieve the number of columns that a line is indented.
get int GetLineIndentation=2127(line line,)

# Retrieve the position before the first non indentation character on a line.
get position GetLineIndentPosition=2128(line line,)

# Retrieve the column number of a position, taking tab width into account.
get position GetColumn=2129(position pos,)

# Count characters between two positions.
fun position CountCharacters=2633(position start, position end)

# Count code units between two positions.
fun position CountCodeUnits=2715(position start, position end)

# Show or hide the horizontal scroll bar.
set void SetHScrollBar=2130(bool visible,)
# Is the horizontal scroll bar visible?
get bool GetHScrollBar=2131(,)

enu IndentView=SC_IV_
val SC_IV_NONE=0
val SC_IV_REAL=1
val SC_IV_LOOKFORWARD=2
val SC_IV_LOOKBOTH=3

ali SC_IV_LOOKFORWARD=LOOK_FORWARD
ali SC_IV_LOOKBOTH=LOOK_BOTH

# Show or hide indentation guides.
set void SetIndentationGuides=2132(IndentView indentView,)

# Are the indentation guides visible?
get IndentView GetIndentationGuides=2133(,)

# Set the highlighted indentation guide column.
# 0 = no highlighted guide.
set void SetHighlightGuide=2134(position column,)

# Get the highlighted indentation guide column.
get position GetHighlightGuide=2135(,)

# Get the position after the last visible characters on a line.
get position GetLineEndPosition=2136(line line,)

# Get the code page used to interpret the bytes of the document as characters.
get int GetCodePage=2137(,)

# Get the foreground colour of the caret.
get colour GetCaretFore=2138(,)

# In read-only mode?
get bool GetReadOnly=2140(,)

# Sets the position of the caret.
set void SetCurrentPos=2141(position caret,)

# Sets the position that starts the selection - this becomes the anchor.
set void SetSelectionStart=2142(position anchor,)

# Returns the position at the start of the selection.
get position GetSelectionStart=2143(,)

# Sets the position that ends the selection - this becomes the caret.
set void SetSelectionEnd=2144(position caret,)

# Returns the position at the end of the selection.
get position GetSelectionEnd=2145(,)

# Set caret to a position, while removing any existing selection.
fun void SetEmptySelection=2556(position caret,)

# Sets the print magnification added to the point size of each style for printing.
set void SetPrintMagnification=2146(int magnification,)

# Returns the print magnification.
get int GetPrintMagnification=2147(,)

enu PrintOption=SC_PRINT_
# PrintColourMode - use same colours as screen.
# with the exception of line number margins, which use a white background
val SC_PRINT_NORMAL=0
# PrintColourMode - invert the light value of each style for printing.
val SC_PRINT_INVERTLIGHT=1
# PrintColourMode - force black text on white background for printing.
val SC_PRINT_BLACKONWHITE=2
# PrintColourMode - text stays coloured, but all background is forced to be white for printing.
val SC_PRINT_COLOURONWHITE=3
# PrintColourMode - only the default-background is forced to be white for printing.
val SC_PRINT_COLOURONWHITEDEFAULTBG=4
# PrintColourMode - use same colours as screen, including line number margins.
val SC_PRINT_SCREENCOLOURS=5

ali SC_PRINT_INVERTLIGHT=INVERT_LIGHT
ali SC_PRINT_BLACKONWHITE=BLACK_ON_WHITE
ali SC_PRINT_COLOURONWHITE=COLOUR_ON_WHITE
ali SC_PRINT_COLOURONWHITEDEFAULTBG=COLOUR_ON_WHITE_DEFAULT_B_G
ali SC_PRINT_SCREENCOLOURS=SCREEN_COLOURS

# Modify colours when printing for clearer printed text.
set void SetPrintColourMode=2148(PrintOption mode,)

# Returns the print colour mode.
get PrintOption GetPrintColourMode=2149(,)

enu FindOption=SCFIND_
val SCFIND_NONE=0x0
val SCFIND_WHOLEWORD=0x2
val SCFIND_MATCHCASE=0x4
val SCFIND_WORDSTART=0x00100000
val SCFIND_REGEXP=0x00200000
val SCFIND_POSIX=0x00400000
val SCFIND_CXX11REGEX=0x00800000

ali SCFIND_WHOLEWORD=WHOLE_WORD
ali SCFIND_MATCHCASE=MATCH_CASE
ali SCFIND_WORDSTART=WORD_START
ali SCFIND_REGEXP=REG_EXP
ali SCFIND_CXX11REGEX=CXX11_REG_EX

# Find some text in the document.
fun position FindText=2150(FindOption searchFlags, findtext ft)

# Find some text in the document.
fun position FindTextFull=2196(FindOption searchFlags, findtextfull ft)

# Draw the document into a display context such as a printer.
fun position FormatRange=2151(bool draw, formatrange fr)

# Draw the document into a display context such as a printer.
fun position FormatRangeFull=2777(bool draw, formatrangefull fr)

enu ChangeHistoryOption=SC_CHANGE_HISTORY_
val SC_CHANGE_HISTORY_DISABLED=0
val SC_CHANGE_HISTORY_ENABLED=1
val SC_CHANGE_HISTORY_MARKERS=2
val SC_CHANGE_HISTORY_INDICATORS=4

# Enable or disable change history.
set void SetChangeHistory=2780(ChangeHistoryOption changeHistory,)

# Report change history status.
get ChangeHistoryOption GetChangeHistory=2781(,)

enu UndoSelectionHistoryOption=SC_UNDO_SELECTION_HISTORY_
val SC_UNDO_SELECTION_HISTORY_DISABLED=0
val SC_UNDO_SELECTION_HISTORY_ENABLED=1

# Enable or disable undo selection history.
set void SetUndoSelectionHistory=2782(UndoSelectionHistoryOption undoSelectionHistory,)

# Report undo selection history status.
get UndoSelectionHistoryOption GetUndoSelectionHistory=2783(,)

# Set selection from serialized form.
set void SetSelectionSerialized=2784(, string selectionString)

# Retrieve serialized form of selection.
get position GetSelectionSerialized=2785(, stringresult selectionString)

# Retrieve the display line at the top of the display.
get line GetFirstVisibleLine=2152(,)

# Retrieve the contents of a line.
# Returns the length of the line.
fun position GetLine=2153(line line, stringresult text)

# Returns the number of lines in the document. There is always at least one.
get line GetLineCount=2154(,)

# Enlarge the number of lines allocated.
set void AllocateLines=2089(line lines,)

# Sets the size in pixels of the left margin.
set void SetMarginLeft=2155(, int pixelWidth)

# Returns the size in pixels of the left margin.
get int GetMarginLeft=2156(,)

# Sets the size in pixels of the right margin.
set void SetMarginRight=2157(, int pixelWidth)

# Returns the size in pixels of the right margin.
get int GetMarginRight=2158(,)

# Is the document different from when it was last saved?
get bool GetModify=2159(,)

# Select a range of text.
fun void SetSel=2160(position anchor, position caret)

# Retrieve the selected text.
# Return the length of the text.
# Result is NUL-terminated.
fun position GetSelText=2161(, stringresult text)

# Retrieve a range of text.
# Return the length of the text.
fun position GetTextRange=2162(, textrange tr)

# Retrieve a range of text that can be past 2GB.
# Return the length of the text.
fun position GetTextRangeFull=2039(, textrangefull tr)

# Draw the selection either highlighted or in normal (non-highlighted) style.
fun void HideSelection=2163(bool hide,)

#Is the selection visible or hidden?
get bool GetSelectionHidden=2088(,)

# Retrieve the x value of the point in the window where a position is displayed.
fun int PointXFromPosition=2164(, position pos)

# Retrieve the y value of the point in the window where a position is displayed.
fun int PointYFromPosition=2165(, position pos)

# Retrieve the line containing a position.
fun line LineFromPosition=2166(position pos,)

# Retrieve the position at the start of a line.
fun position PositionFromLine=2167(line line,)

# Scroll horizontally and vertically.
fun void LineScroll=2168(position columns, line lines)

# Ensure the caret is visible.
fun void ScrollCaret=2169(,)

# Scroll the argument positions and the range between them into view giving
# priority to the primary position then the secondary position.
# This may be used to make a search match visible.
fun void ScrollRange=2569(position secondary, position primary)

# Replace the selected text with the argument text.
fun void ReplaceSel=2170(, string text)

# Set to read only or read write.
set void SetReadOnly=2171(bool readOnly,)

# Null operation.
fun void Null=2172(,)

# Will a paste succeed?
fun bool CanPaste=2173(,)

# Are there any undoable actions in the undo history?
fun bool CanUndo=2174(,)

# Delete the undo history.
fun void EmptyUndoBuffer=2175(,)

# Undo one action in the undo history.
fun void Undo=2176(,)

# Cut the selection to the clipboard.
fun void Cut=2177(,)

# Copy the selection to the clipboard.
fun void Copy=2178(,)

# Paste the contents of the clipboard into the document replacing the selection.
fun void Paste=2179(,)

# Clear the selection.
fun void Clear=2180(,)

# Replace the contents of the document with the argument text.
fun void SetText=2181(, string text)

# Retrieve all the text in the document.
# Returns number of characters retrieved.
# Result is NUL-terminated.
fun position GetText=2182(position length, stringresult text)

# Retrieve the number of characters in the document.
get position GetTextLength=2183(,)

# Retrieve a pointer to a function that processes messages for this Scintilla.
get pointer GetDirectFunction=2184(,)

# Retrieve a pointer to a function that processes messages for this Scintilla and returns status.
get pointer GetDirectStatusFunction=2772(,)

# Retrieve a pointer value to use as the first argument when calling
# the function returned by GetDirectFunction.
get pointer GetDirectPointer=2185(,)

# Set to overtype (true) or insert mode.
set void SetOvertype=2186(bool overType,)

# Returns true if overtype mode is active otherwise false is returned.
get bool GetOvertype=2187(,)

# Set the width of the insert mode caret.
set void SetCaretWidth=2188(int pixelWidth,)

# Returns the width of the insert mode caret.
get int GetCaretWidth=2189(,)

# Sets the position that starts the target which is used for updating the
# document without affecting the scroll position.
set void SetTargetStart=2190(position start,)

# Get the position that starts the target.
get position GetTargetStart=2191(,)

# Sets the virtual space of the target start
set void SetTargetStartVirtualSpace=2728(position space,)

# Get the virtual space of the target start
get position GetTargetStartVirtualSpace=2729(,)

# Sets the position that ends the target which is used for updating the
# document without affecting the scroll position.
set void SetTargetEnd=2192(position end,)

# Get the position that ends the target.
get position GetTargetEnd=2193(,)

# Sets the virtual space of the target end
set void SetTargetEndVirtualSpace=2730(position space,)

# Get the virtual space of the target end
get position GetTargetEndVirtualSpace=2731(,)

# Sets both the start and end of the target in one call.
fun void SetTargetRange=2686(position start, position end)

# Retrieve the text in the target.
get position GetTargetText=2687(, stringresult text)

# Make the target range start and end be the same as the selection range start and end.
fun void TargetFromSelection=2287(,)

# Sets the target to the whole document.
fun void TargetWholeDocument=2690(,)

# Replace the target text with the argument text.
# Text is counted so it can contain NULs.
# Returns the length of the replacement text.
fun position ReplaceTarget=2194(position length, string text)

# Replace the target text with the argument text after \d processing.
# Text is counted so it can contain NULs.
# Looks for \d where d is between 1 and 9 and replaces these with the strings
# matched in the last search operation which were surrounded by \( and \).
# Returns the length of the replacement text including any change
# caused by processing the \d patterns.
fun position ReplaceTargetRE=2195(position length, string text)

# Replace the target text with the argument text but ignore prefix and suffix that
# are the same as current.
fun position ReplaceTargetMinimal=2779(position length, string text)

# Search for a counted string in the target and set the target to the found
# range. Text is counted so it can contain NULs.
# Returns start of found range or -1 for failure in which case target is not moved.
fun position SearchInTarget=2197(position length, string text)

# Set the search flags used by SearchInTarget.
set void SetSearchFlags=2198(FindOption searchFlags,)

# Get the search flags used by SearchInTarget.
get FindOption GetSearchFlags=2199(,)

# Show a call tip containing a definition near position pos.
fun void CallTipShow=2200(position pos, string definition)

# Remove the call tip from the screen.
fun void CallTipCancel=2201(,)

# Is there an active call tip?
fun bool CallTipActive=2202(,)

# Retrieve the position where the caret was before displaying the call tip.
fun position CallTipPosStart=2203(,)

# Set the start position in order to change when backspacing removes the calltip.
set void CallTipSetPosStart=2214(position posStart,)

# Highlight a segment of the definition.
fun void CallTipSetHlt=2204(position highlightStart, position highlightEnd)

# Set the background colour for the call tip.
set void CallTipSetBack=2205(colour back,)

# Set the foreground colour for the call tip.
set void CallTipSetFore=2206(colour fore,)

# Set the foreground colour for the highlighted part of the call tip.
set void CallTipSetForeHlt=2207(colour fore,)

# Enable use of STYLE_CALLTIP and set call tip tab size in pixels.
set void CallTipUseStyle=2212(int tabSize,)

# Set position of calltip, above or below text.
set void CallTipSetPosition=2213(bool above,)

# Find the display line of a document line taking hidden lines into account.
fun line VisibleFromDocLine=2220(line docLine,)

# Find the document line of a display line taking hidden lines into account.
fun line DocLineFromVisible=2221(line displayLine,)

# The number of display lines needed to wrap a document line
fun line WrapCount=2235(line docLine,)

enu FoldLevel=SC_FOLDLEVEL
val SC_FOLDLEVELNONE=0x0
val SC_FOLDLEVELBASE=0x400
val SC_FOLDLEVELWHITEFLAG=0x1000
val SC_FOLDLEVELHEADERFLAG=0x2000
val SC_FOLDLEVELNUMBERMASK=0x0FFF

ali SC_FOLDLEVELWHITEFLAG=WHITE_FLAG
ali SC_FOLDLEVELHEADERFLAG=HEADER_FLAG
ali SC_FOLDLEVELNUMBERMASK=NUMBER_MASK

# Set the fold level of a line.
# This encodes an integer level along with flags indicating whether the
# line is a header and whether it is effectively white space.
set void SetFoldLevel=2222(line line, FoldLevel level)

# Retrieve the fold level of a line.
get FoldLevel GetFoldLevel=2223(line line,)

# Find the last child line of a header line.
get line GetLastChild=2224(line line, FoldLevel level)

# Find the parent line of a child line.
get line GetFoldParent=2225(line line,)

# Make a range of lines visible.
fun void ShowLines=2226(line lineStart, line lineEnd)

# Make a range of lines invisible.
fun void HideLines=2227(line lineStart, line lineEnd)

# Is a line visible?
get bool GetLineVisible=2228(line line,)

# Are all lines visible?
get bool GetAllLinesVisible=2236(,)

# Show the children of a header line.
set void SetFoldExpanded=2229(line line, bool expanded)

# Is a header line expanded?
get bool GetFoldExpanded=2230(line line,)

# Switch a header line between expanded and contracted.
fun void ToggleFold=2231(line line,)

# Switch a header line between expanded and contracted and show some text after the line.
fun void ToggleFoldShowText=2700(line line, string text)

enu FoldDisplayTextStyle=SC_FOLDDISPLAYTEXT_
val SC_FOLDDISPLAYTEXT_HIDDEN=0
val SC_FOLDDISPLAYTEXT_STANDARD=1
val SC_FOLDDISPLAYTEXT_BOXED=2

# Set the style of fold display text.
set void FoldDisplayTextSetStyle=2701(FoldDisplayTextStyle style,)

# Get the style of fold display text.
get FoldDisplayTextStyle FoldDisplayTextGetStyle=2707(,)

# Set the default fold display text.
fun void SetDefaultFoldDisplayText=2722(, string text)

# Get the default fold display text.
fun int GetDefaultFoldDisplayText=2723(, stringresult text)

enu FoldAction=SC_FOLDACTION_
val SC_FOLDACTION_CONTRACT=0
val SC_FOLDACTION_EXPAND=1
val SC_FOLDACTION_TOGGLE=2
val SC_FOLDACTION_CONTRACT_EVERY_LEVEL=4

# Expand or contract a fold header.
fun void FoldLine=2237(line line, FoldAction action)

# Expand or contract a fold header and its children.
fun void FoldChildren=2238(line line, FoldAction action)

# Expand a fold header and all children. Use the level argument instead of the line's current level.
fun void ExpandChildren=2239(line line, FoldLevel level)

# Expand or contract all fold headers.
fun void FoldAll=2662(FoldAction action,)

# Ensure a particular line is visible by expanding any header line hiding it.
fun void EnsureVisible=2232(line line,)

enu AutomaticFold=SC_AUTOMATICFOLD_
val SC_AUTOMATICFOLD_NONE=0x0000
val SC_AUTOMATICFOLD_SHOW=0x0001
val SC_AUTOMATICFOLD_CLICK=0x0002
val SC_AUTOMATICFOLD_CHANGE=0x0004

# Set automatic folding behaviours.
set void SetAutomaticFold=2663(AutomaticFold automaticFold,)

# Get automatic folding behaviours.
get AutomaticFold GetAutomaticFold=2664(,)

enu FoldFlag=SC_FOLDFLAG_
val SC_FOLDFLAG_NONE=0x0000
val SC_FOLDFLAG_LINEBEFORE_EXPANDED=0x0002
val SC_FOLDFLAG_LINEBEFORE_CONTRACTED=0x0004
val SC_FOLDFLAG_LINEAFTER_EXPANDED=0x0008
val SC_FOLDFLAG_LINEAFTER_CONTRACTED=0x0010
val SC_FOLDFLAG_LEVELNUMBERS=0x0040
val SC_FOLDFLAG_LINESTATE=0x0080

ali SC_FOLDFLAG_LINEBEFORE_EXPANDED=LINE_BEFORE_EXPANDED
ali SC_FOLDFLAG_LINEBEFORE_CONTRACTED=LINE_BEFORE_CONTRACTED
ali SC_FOLDFLAG_LINEAFTER_EXPANDED=LINE_AFTER_EXPANDED
ali SC_FOLDFLAG_LINEAFTER_CONTRACTED=LINE_AFTER_CONTRACTED
ali SC_FOLDFLAG_LEVELNUMBERS=LEVEL_NUMBERS
ali SC_FOLDFLAG_LINESTATE=LINE_STATE

# Set some style options for folding.
set void SetFoldFlags=2233(FoldFlag flags,)

# Ensure a particular line is visible by expanding any header line hiding it.
# Use the currently set visibility policy to determine which range to display.
fun void EnsureVisibleEnforcePolicy=2234(line line,)

# Sets whether a tab pressed when caret is within indentation indents.
set void SetTabIndents=2260(bool tabIndents,)

# Does a tab pressed when caret is within indentation indent?
get bool GetTabIndents=2261(,)

# Sets whether a backspace pressed when caret is within indentation unindents.
set void SetBackSpaceUnIndents=2262(bool bsUnIndents,)

# Does a backspace pressed when caret is within indentation unindent?
get bool GetBackSpaceUnIndents=2263(,)

val SC_TIME_FOREVER=10000000

# Sets the time the mouse must sit still to generate a mouse dwell event.
set void SetMouseDwellTime=2264(int periodMilliseconds,)

# Retrieve the time the mouse must sit still to generate a mouse dwell event.
get int GetMouseDwellTime=2265(,)

# Get position of start of word.
fun position WordStartPosition=2266(position pos, bool onlyWordCharacters)

# Get position of end of word.
fun position WordEndPosition=2267(position pos, bool onlyWordCharacters)

# Is the range start..end considered a word?
fun bool IsRangeWord=2691(position start, position end)

enu IdleStyling=SC_IDLESTYLING_
val SC_IDLESTYLING_NONE=0
val SC_IDLESTYLING_TOVISIBLE=1
val SC_IDLESTYLING_AFTERVISIBLE=2
val SC_IDLESTYLING_ALL=3

ali SC_IDLESTYLING_TOVISIBLE=TO_VISIBLE
ali SC_IDLESTYLING_AFTERVISIBLE=AFTER_VISIBLE

# Sets limits to idle styling.
set void SetIdleStyling=2692(IdleStyling idleStyling,)

# Retrieve the limits to idle styling.
get IdleStyling GetIdleStyling=2693(,)

enu Wrap=SC_WRAP_
val SC_WRAP_NONE=0
val SC_WRAP_WORD=1
val SC_WRAP_CHAR=2
val SC_WRAP_WHITESPACE=3

ali SC_WRAP_WHITESPACE=WHITE_SPACE

# Sets whether text is word wrapped.
set void SetWrapMode=2268(Wrap wrapMode,)

# Retrieve whether text is word wrapped.
get Wrap GetWrapMode=2269(,)

enu WrapVisualFlag=SC_WRAPVISUALFLAG_
val SC_WRAPVISUALFLAG_NONE=0x0000
val SC_WRAPVISUALFLAG_END=0x0001
val SC_WRAPVISUALFLAG_START=0x0002
val SC_WRAPVISUALFLAG_MARGIN=0x0004

# Set the display mode of visual flags for wrapped lines.
set void SetWrapVisualFlags=2460(WrapVisualFlag wrapVisualFlags,)

# Retrive the display mode of visual flags for wrapped lines.
get WrapVisualFlag GetWrapVisualFlags=2461(,)

enu WrapVisualLocation=SC_WRAPVISUALFLAGLOC_
val SC_WRAPVISUALFLAGLOC_DEFAULT=0x0000
val SC_WRAPVISUALFLAGLOC_END_BY_TEXT=0x0001
val SC_WRAPVISUALFLAGLOC_START_BY_TEXT=0x0002

# Set the location of visual flags for wrapped lines.
set void SetWrapVisualFlagsLocation=2462(WrapVisualLocation wrapVisualFlagsLocation,)

# Retrive the location of visual flags for wrapped lines.
get WrapVisualLocation GetWrapVisualFlagsLocation=2463(,)

# Set the start indent for wrapped lines.
set void SetWrapStartIndent=2464(int indent,)

# Retrive the start indent for wrapped lines.
get int GetWrapStartIndent=2465(,)

enu WrapIndentMode=SC_WRAPINDENT_
val SC_WRAPINDENT_FIXED=0
val SC_WRAPINDENT_SAME=1
val SC_WRAPINDENT_INDENT=2
val SC_WRAPINDENT_DEEPINDENT=3

ali SC_WRAPINDENT_DEEPINDENT=DEEP_INDENT

# Sets how wrapped sublines are placed. Default is fixed.
set void SetWrapIndentMode=2472(WrapIndentMode wrapIndentMode,)

# Retrieve how wrapped sublines are placed. Default is fixed.
get WrapIndentMode GetWrapIndentMode=2473(,)

enu LineCache=SC_CACHE_
val SC_CACHE_NONE=0
val SC_CACHE_CARET=1
val SC_CACHE_PAGE=2
val SC_CACHE_DOCUMENT=3

# Sets the degree of caching of layout information.
set void SetLayoutCache=2272(LineCache cacheMode,)

# Retrieve the degree of caching of layout information.
get LineCache GetLayoutCache=2273(,)

# Sets the document width assumed for scrolling.
set void SetScrollWidth=2274(int pixelWidth,)

# Retrieve the document width assumed for scrolling.
get int GetScrollWidth=2275(,)

# Sets whether the maximum width line displayed is used to set scroll width.
set void SetScrollWidthTracking=2516(bool tracking,)

# Retrieve whether the scroll width tracks wide lines.
get bool GetScrollWidthTracking=2517(,)

# Measure the pixel width of some text in a particular style.
# NUL terminated text argument.
# Does not handle tab or control characters.
fun int TextWidth=2276(int style, string text)

# Sets the scroll range so that maximum scroll position has
# the last line at the bottom of the view (default).
# Setting this to false allows scrolling one page below the last line.
set void SetEndAtLastLine=2277(bool endAtLastLine,)

# Retrieve whether the maximum scroll position has the last
# line at the bottom of the view.
get bool GetEndAtLastLine=2278(,)

# Retrieve the height of a particular line of text in pixels.
fun int TextHeight=2279(line line,)

# Show or hide the vertical scroll bar.
set void SetVScrollBar=2280(bool visible,)

# Is the vertical scroll bar visible?
get bool GetVScrollBar=2281(,)

# Append a string to the end of the document without changing the selection.
fun void AppendText=2282(position length, string text)

enu PhasesDraw=SC_PHASES_
val SC_PHASES_ONE=0
val SC_PHASES_TWO=1
val SC_PHASES_MULTIPLE=2

# How many phases is drawing done in?
get PhasesDraw GetPhasesDraw=2673(,)

# In one phase draw, text is drawn in a series of rectangular blocks with no overlap.
# In two phase draw, text is drawn in a series of lines allowing runs to overlap horizontally.
# In multiple phase draw, each element is drawn over the whole drawing area, allowing text
# to overlap from one line to the next.
set void SetPhasesDraw=2674(PhasesDraw phases,)

# Control font anti-aliasing.

enu FontQuality=SC_EFF_
val SC_EFF_QUALITY_MASK=0xF
val SC_EFF_QUALITY_DEFAULT=0
val SC_EFF_QUALITY_NON_ANTIALIASED=1
val SC_EFF_QUALITY_ANTIALIASED=2
val SC_EFF_QUALITY_LCD_OPTIMIZED=3

# Choose the quality level for text from the FontQuality enumeration.
set void SetFontQuality=2611(FontQuality fontQuality,)

# Retrieve the quality level for text.
get FontQuality GetFontQuality=2612(,)

# Scroll so that a display line is at the top of the display.
set void SetFirstVisibleLine=2613(line displayLine,)

enu MultiPaste=SC_MULTIPASTE_
val SC_MULTIPASTE_ONCE=0
val SC_MULTIPASTE_EACH=1

# Change the effect of pasting when there are multiple selections.
set void SetMultiPaste=2614(MultiPaste multiPaste,)

# Retrieve the effect of pasting when there are multiple selections.
get MultiPaste GetMultiPaste=2615(,)

# Retrieve the value of a tag from a regular expression search.
# Result is NUL-terminated.
get int GetTag=2616(int tagNumber, stringresult tagValue)

# Join the lines in the target.
fun void LinesJoin=2288(,)

# Split the lines in the target into lines that are less wide than pixelWidth
# where possible.
fun void LinesSplit=2289(int pixelWidth,)

# Set one of the colours used as a chequerboard pattern in the fold margin
fun void SetFoldMarginColour=2290(bool useSetting, colour back)
# Set the other colour used as a chequerboard pattern in the fold margin
fun void SetFoldMarginHiColour=2291(bool useSetting, colour fore)

enu Accessibility=SC_ACCESSIBILITY_
val SC_ACCESSIBILITY_DISABLED=0
val SC_ACCESSIBILITY_ENABLED=1

# Enable or disable accessibility.
set void SetAccessibility=2702(Accessibility accessibility,)

# Report accessibility status.
get Accessibility GetAccessibility=2703(,)

## New messages go here

## Start of key messages
# Move caret down one line.
fun void LineDown=2300(,)

# Move caret down one line extending selection to new caret position.
fun void LineDownExtend=2301(,)

# Move caret up one line.
fun void LineUp=2302(,)

# Move caret up one line extending selection to new caret position.
fun void LineUpExtend=2303(,)

# Move caret left one character.
fun void CharLeft=2304(,)

# Move caret left one character extending selection to new caret position.
fun void CharLeftExtend=2305(,)

# Move caret right one character.
fun void CharRight=2306(,)

# Move caret right one character extending selection to new caret position.
fun void CharRightExtend=2307(,)

# Move caret left one word.
fun void WordLeft=2308(,)

# Move caret left one word extending selection to new caret position.
fun void WordLeftExtend=2309(,)

# Move caret right one word.
fun void WordRight=2310(,)

# Move caret right one word extending selection to new caret position.
fun void WordRightExtend=2311(,)

# Move caret to first position on line.
fun void Home=2312(,)

# Move caret to first position on line extending selection to new caret position.
fun void HomeExtend=2313(,)

# Move caret to last position on line.
fun void LineEnd=2314(,)

# Move caret to last position on line extending selection to new caret position.
fun void LineEndExtend=2315(,)

# Move caret to first position in document.
fun void DocumentStart=2316(,)

# Move caret to first position in document extending selection to new caret position.
fun void DocumentStartExtend=2317(,)

# Move caret to last position in document.
fun void DocumentEnd=2318(,)

# Move caret to last position in document extending selection to new caret position.
fun void DocumentEndExtend=2319(,)

# Move caret one page up.
fun void PageUp=2320(,)

# Move caret one page up extending selection to new caret position.
fun void PageUpExtend=2321(,)

# Move caret one page down.
fun void PageDown=2322(,)

# Move caret one page down extending selection to new caret position.
fun void PageDownExtend=2323(,)

# Switch from insert to overtype mode or the reverse.
fun void EditToggleOvertype=2324(,)

# Cancel any modes such as call tip or auto-completion list display.
fun void Cancel=2325(,)

# Delete the selection or if no selection, the character before the caret.
fun void DeleteBack=2326(,)

# If selection is empty or all on one line replace the selection with a tab character.
# If more than one line selected, indent the lines.
fun void Tab=2327(,)

# Indent the current and selected lines.
fun void LineIndent=2813(,)

# If selection is empty or all on one line dedent the line if caret is at start, else move caret.
# If more than one line selected, dedent the lines.
fun void BackTab=2328(,)

# Dedent the current and selected lines.
fun void LineDedent=2814(,)

# Insert a new line, may use a CRLF, CR or LF depending on EOL mode.
fun void NewLine=2329(,)

# Insert a Form Feed character.
fun void FormFeed=2330(,)

# Move caret to before first visible character on line.
# If already there move to first character on line.
fun void VCHome=2331(,)

# Like VCHome but extending selection to new caret position.
fun void VCHomeExtend=2332(,)

# Magnify the displayed text by increasing the sizes by 1 point.
fun void ZoomIn=2333(,)

# Make the displayed text smaller by decreasing the sizes by 1 point.
fun void ZoomOut=2334(,)

# Delete the word to the left of the caret.
fun void DelWordLeft=2335(,)

# Delete the word to the right of the caret.
fun void DelWordRight=2336(,)

# Delete the word to the right of the caret, but not the trailing non-word characters.
fun void DelWordRightEnd=2518(,)

# Cut the line containing the caret.
fun void LineCut=2337(,)

# Delete the line containing the caret.
fun void LineDelete=2338(,)

# Switch the current line with the previous.
fun void LineTranspose=2339(,)

# Reverse order of selected lines.
fun void LineReverse=2354(,)

# Duplicate the current line.
fun void LineDuplicate=2404(,)

# Transform the selection to lower case.
fun void LowerCase=2340(,)

# Transform the selection to upper case.
fun void UpperCase=2341(,)

# Scroll the document down, keeping the caret visible.
fun void LineScrollDown=2342(,)

# Scroll the document up, keeping the caret visible.
fun void LineScrollUp=2343(,)

# Delete the selection or if no selection, the character before the caret.
# Will not delete the character before at the start of a line.
fun void DeleteBackNotLine=2344(,)

# Move caret to first position on display line.
fun void HomeDisplay=2345(,)

# Move caret to first position on display line extending selection to
# new caret position.
fun void HomeDisplayExtend=2346(,)

# Move caret to last position on display line.
fun void LineEndDisplay=2347(,)

# Move caret to last position on display line extending selection to new
# caret position.
fun void LineEndDisplayExtend=2348(,)

# Like Home but when word-wrap is enabled goes first to start of display line
# HomeDisplay, then to start of document line Home.
fun void HomeWrap=2349(,)

# Like HomeExtend but when word-wrap is enabled extends first to start of display line
# HomeDisplayExtend, then to start of document line HomeExtend.
fun void HomeWrapExtend=2450(,)

# Like LineEnd but when word-wrap is enabled goes first to end of display line
# LineEndDisplay, then to start of document line LineEnd.
fun void LineEndWrap=2451(,)

# Like LineEndExtend but when word-wrap is enabled extends first to end of display line
# LineEndDisplayExtend, then to start of document line LineEndExtend.
fun void LineEndWrapExtend=2452(,)

# Like VCHome but when word-wrap is enabled goes first to start of display line
# VCHomeDisplay, then behaves like VCHome.
fun void VCHomeWrap=2453(,)

# Like VCHomeExtend but when word-wrap is enabled extends first to start of display line
# VCHomeDisplayExtend, then behaves like VCHomeExtend.
fun void VCHomeWrapExtend=2454(,)

# Copy the line containing the caret.
fun void LineCopy=2455(,)

# Move the caret inside current view if it's not there already.
fun void MoveCaretInsideView=2401(,)

# How many characters are on a line, including end of line characters?
fun position LineLength=2350(line line,)

# Highlight the characters at two positions.
fun void BraceHighlight=2351(position posA, position posB)

# Use specified indicator to highlight matching braces instead of changing their style.
fun void BraceHighlightIndicator=2498(bool useSetting, int indicator)

# Highlight the character at a position indicating there is no matching brace.
fun void BraceBadLight=2352(position pos,)

# Use specified indicator to highlight non matching brace instead of changing its style.
fun void BraceBadLightIndicator=2499(bool useSetting, int indicator)

# Find the position of a matching brace or INVALID_POSITION if no match.
# The maxReStyle must be 0 for now. It may be defined in a future release.
fun position BraceMatch=2353(position pos, int maxReStyle)

# Similar to BraceMatch, but matching starts at the explicit start position.
fun position BraceMatchNext=2369(position pos, position startPos)

# Are the end of line characters visible?
get bool GetViewEOL=2355(,)

# Make the end of line characters visible or invisible.
set void SetViewEOL=2356(bool visible,)

# Retrieve a pointer to the document object.
get pointer GetDocPointer=2357(,)

# Change the document object used.
set void SetDocPointer=2358(, pointer doc)

# Set which document modification events are sent to the container.
set void SetModEventMask=2359(ModificationFlags eventMask,)

enu EdgeVisualStyle=EDGE_
val EDGE_NONE=0
val EDGE_LINE=1
val EDGE_BACKGROUND=2
val EDGE_MULTILINE=3

ali EDGE_MULTILINE=MULTI_LINE

# Retrieve the column number which text should be kept within.
get position GetEdgeColumn=2360(,)

# Set the column number of the edge.
# If text goes past the edge then it is highlighted.
set void SetEdgeColumn=2361(position column,)

# Retrieve the edge highlight mode.
get EdgeVisualStyle GetEdgeMode=2362(,)

# The edge may be displayed by a line (EDGE_LINE/EDGE_MULTILINE) or by highlighting text that
# goes beyond it (EDGE_BACKGROUND) or not displayed at all (EDGE_NONE).
set void SetEdgeMode=2363(EdgeVisualStyle edgeMode,)

# Retrieve the colour used in edge indication.
get colour GetEdgeColour=2364(,)

# Change the colour used in edge indication.
set void SetEdgeColour=2365(colour edgeColour,)

# Add a new vertical edge to the view.
fun void MultiEdgeAddLine=2694(position column, colour edgeColour)

# Clear all vertical edges.
fun void MultiEdgeClearAll=2695(,)

# Get multi edge positions.
get position GetMultiEdgeColumn=2749(int which,)

# Sets the current caret position to be the search anchor.
fun void SearchAnchor=2366(,)

# Find some text starting at the search anchor.
# Does not ensure the selection is visible.
fun position SearchNext=2367(FindOption searchFlags, string text)

# Find some text starting at the search anchor and moving backwards.
# Does not ensure the selection is visible.
fun position SearchPrev=2368(FindOption searchFlags, string text)

# Retrieves the number of lines completely visible.
get line LinesOnScreen=2370(,)

enu PopUp=SC_POPUP_
val SC_POPUP_NEVER=0
val SC_POPUP_ALL=1
val SC_POPUP_TEXT=2

# Set whether a pop up menu is displayed automatically when the user presses
# the wrong mouse button on certain areas.
fun void UsePopUp=2371(PopUp popUpMode,)

# Is the selection rectangular? The alternative is the more common stream selection.
get bool SelectionIsRectangle=2372(,)

# Set the zoom level. This number of points is added to the size of all fonts.
# It may be positive to magnify or negative to reduce.
set void SetZoom=2373(int zoomInPoints,)
# Retrieve the zoom level.
get int GetZoom=2374(,)

enu DocumentOption=SC_DOCUMENTOPTION_
val SC_DOCUMENTOPTION_DEFAULT=0
val SC_DOCUMENTOPTION_STYLES_NONE=0x1
val SC_DOCUMENTOPTION_TEXT_LARGE=0x100

# Create a new document object.
# Starts with reference count of 1 and not selected into editor.
fun pointer CreateDocument=2375(position bytes, DocumentOption documentOptions)
# Extend life of document.
fun void AddRefDocument=2376(, pointer doc)
# Release a reference to the document, deleting document if it fades to black.
fun void ReleaseDocument=2377(, pointer doc)

# Get which document options are set.
get DocumentOption GetDocumentOptions=2379(,)

# Get which document modification events are sent to the container.
get ModificationFlags GetModEventMask=2378(,)

# Set whether command events are sent to the container.
set void SetCommandEvents=2717(bool commandEvents,)

# Get whether command events are sent to the container.
get bool GetCommandEvents=2718(,)

# Change internal focus flag.
set void SetFocus=2380(bool focus,)
# Get internal focus flag.
get bool GetFocus=2381(,)

enu Status=SC_STATUS_
val SC_STATUS_OK=0
val SC_STATUS_FAILURE=1
val SC_STATUS_BADALLOC=2
val SC_STATUS_WARN_START=1000
val SC_STATUS_WARN_REGEX=1001

ali SC_STATUS_BADALLOC=BAD_ALLOC
ali SC_STATUS_WARN_REGEX=REG_EX

# Change error status - 0 = OK.
set void SetStatus=2382(Status status,)
# Get error status.
get Status GetStatus=2383(,)

# Set whether the mouse is captured when its button is pressed.
set void SetMouseDownCaptures=2384(bool captures,)
# Get whether mouse gets captured.
get bool GetMouseDownCaptures=2385(,)

# Set whether the mouse wheel can be active outside the window.
set void SetMouseWheelCaptures=2696(bool captures,)
# Get whether mouse wheel can be active outside the window.
get bool GetMouseWheelCaptures=2697(,)

# Sets the cursor to one of the SC_CURSOR* values.
set void SetCursor=2386(CursorShape cursorType,)
# Get cursor type.
get CursorShape GetCursor=2387(,)

# Change the way control characters are displayed:
# If symbol is < 32, keep the drawn way, else, use the given character.
set void SetControlCharSymbol=2388(int symbol,)
# Get the way control characters are displayed.
get int GetControlCharSymbol=2389(,)

# Move to the previous change in capitalisation.
fun void WordPartLeft=2390(,)
# Move to the previous change in capitalisation extending selection
# to new caret position.
fun void WordPartLeftExtend=2391(,)
# Move to the change next in capitalisation.
fun void WordPartRight=2392(,)
# Move to the next change in capitalisation extending selection
# to new caret position.
fun void WordPartRightExtend=2393(,)

# Constants for use with SetVisiblePolicy, similar to SetCaretPolicy.
enu VisiblePolicy=VISIBLE_
val VISIBLE_SLOP=0x01
val VISIBLE_STRICT=0x04

# Set the way the display area is determined when a particular line
# is to be moved to by Find, FindNext, GotoLine, etc.
fun void SetVisiblePolicy=2394(VisiblePolicy visiblePolicy, int visibleSlop)

# Delete back from the current position to the start of the line.
fun void DelLineLeft=2395(,)

# Delete forwards from the current position to the end of the line.
fun void DelLineRight=2396(,)

# Set the xOffset (ie, horizontal scroll position).
set void SetXOffset=2397(int xOffset,)

# Get the xOffset (ie, horizontal scroll position).
get int GetXOffset=2398(,)

# Set the last x chosen value to be the caret x position.
fun void ChooseCaretX=2399(,)

# Set the focus to this Scintilla widget.
fun void GrabFocus=2400(,)

enu CaretPolicy=CARET_
# Caret policy, used by SetXCaretPolicy and SetYCaretPolicy.
# If CARET_SLOP is set, we can define a slop value: caretSlop.
# This value defines an unwanted zone (UZ) where the caret is... unwanted.
# This zone is defined as a number of pixels near the vertical margins,
# and as a number of lines near the horizontal margins.
# By keeping the caret away from the edges, it is seen within its context,
# so it is likely that the identifier that the caret is on can be completely seen,
# and that the current line is seen with some of the lines following it which are
# often dependent on that line.
val CARET_SLOP=0x01
# If CARET_STRICT is set, the policy is enforced... strictly.
# The caret is centred on the display if slop is not set,
# and cannot go in the UZ if slop is set.
val CARET_STRICT=0x04
# If CARET_JUMPS is set, the display is moved more energetically
# so the caret can move in the same direction longer before the policy is applied again.
val CARET_JUMPS=0x10
# If CARET_EVEN is not set, instead of having symmetrical UZs,
# the left and bottom UZs are extended up to right and top UZs respectively.
# This way, we favour the displaying of useful information: the beginning of lines,
# where most code reside, and the lines after the caret, eg. the body of a function.
val CARET_EVEN=0x08

# Set the way the caret is kept visible when going sideways.
# The exclusion zone is given in pixels.
fun void SetXCaretPolicy=2402(CaretPolicy caretPolicy, int caretSlop)

# Set the way the line the caret is on is kept visible.
# The exclusion zone is given in lines.
fun void SetYCaretPolicy=2403(CaretPolicy caretPolicy, int caretSlop)

# Set printing to line wrapped (SC_WRAP_WORD) or not line wrapped (SC_WRAP_NONE).
set void SetPrintWrapMode=2406(Wrap wrapMode,)

# Is printing line wrapped?
get Wrap GetPrintWrapMode=2407(,)

# Set a fore colour for active hotspots.
set void SetHotspotActiveFore=2410(bool useSetting, colour fore)

# Get the fore colour for active hotspots.
get colour GetHotspotActiveFore=2494(,)

# Set a back colour for active hotspots.
set void SetHotspotActiveBack=2411(bool useSetting, colour back)

# Get the back colour for active hotspots.
get colour GetHotspotActiveBack=2495(,)

# Enable / Disable underlining active hotspots.
set void SetHotspotActiveUnderline=2412(bool underline,)

# Get whether underlining for active hotspots.
get bool GetHotspotActiveUnderline=2496(,)

# Limit hotspots to single line so hotspots on two lines don't merge.
set void SetHotspotSingleLine=2421(bool singleLine,)

# Get the HotspotSingleLine property
get bool GetHotspotSingleLine=2497(,)

# Move caret down one paragraph (delimited by empty lines).
fun void ParaDown=2413(,)

# Extend selection down one paragraph (delimited by empty lines).
fun void ParaDownExtend=2414(,)

# Move caret up one paragraph (delimited by empty lines).
fun void ParaUp=2415(,)

# Extend selection up one paragraph (delimited by empty lines).
fun void ParaUpExtend=2416(,)

# Given a valid document position, return the previous position taking code
# page into account. Returns 0 if passed 0.
fun position PositionBefore=2417(position pos,)

# Given a valid document position, return the next position taking code
# page into account. Maximum value returned is the last position in the document.
fun position PositionAfter=2418(position pos,)

# Given a valid document position, return a position that differs in a number
# of characters. Returned value is always between 0 and last position in document.
fun position PositionRelative=2670(position pos, position relative)

# Given a valid document position, return a position that differs in a number
# of UTF-16 code units. Returned value is always between 0 and last position in document.
# The result may point half way (2 bytes) inside a non-BMP character.
fun position PositionRelativeCodeUnits=2716(position pos, position relative)

# Copy a range of text to the clipboard. Positions are clipped into the document.
fun void CopyRange=2419(position start, position end)

# Copy argument text to the clipboard.
fun void CopyText=2420(position length, string text)

enu SelectionMode=SC_SEL_
val SC_SEL_STREAM=0
val SC_SEL_RECTANGLE=1
val SC_SEL_LINES=2
val SC_SEL_THIN=3

# Set the selection mode to stream (SC_SEL_STREAM) or rectangular (SC_SEL_RECTANGLE/SC_SEL_THIN) or
# by lines (SC_SEL_LINES).
set void SetSelectionMode=2422(SelectionMode selectionMode,)

# Set the selection mode to stream (SC_SEL_STREAM) or rectangular (SC_SEL_RECTANGLE/SC_SEL_THIN) or
# by lines (SC_SEL_LINES) without changing MoveExtendsSelection.
fun void ChangeSelectionMode=2659(SelectionMode selectionMode,)

# Get the mode of the current selection.
get SelectionMode GetSelectionMode=2423(,)

# Set whether or not regular caret moves will extend or reduce the selection.
set void SetMoveExtendsSelection=2719(bool moveExtendsSelection,)

# Get whether or not regular caret moves will extend or reduce the selection.
get bool GetMoveExtendsSelection=2706(,)

# Retrieve the position of the start of the selection at the given line (INVALID_POSITION if no selection on this line).
fun position GetLineSelStartPosition=2424(line line,)

# Retrieve the position of the end of the selection at the given line (INVALID_POSITION if no selection on this line).
fun position GetLineSelEndPosition=2425(line line,)

## RectExtended rectangular selection moves
# Move caret down one line, extending rectangular selection to new caret position.
fun void LineDownRectExtend=2426(,)

# Move caret up one line, extending rectangular selection to new caret position.
fun void LineUpRectExtend=2427(,)

# Move caret left one character, extending rectangular selection to new caret position.
fun void CharLeftRectExtend=2428(,)

# Move caret right one character, extending rectangular selection to new caret position.
fun void CharRightRectExtend=2429(,)

# Move caret to first position on line, extending rectangular selection to new caret position.
fun void HomeRectExtend=2430(,)

# Move caret to before first visible character on line.
# If already there move to first character on line.
# In either case, extend rectangular selection to new caret position.
fun void VCHomeRectExtend=2431(,)

# Move caret to last position on line, extending rectangular selection to new caret position.
fun void LineEndRectExtend=2432(,)

# Move caret one page up, extending rectangular selection to new caret position.
fun void PageUpRectExtend=2433(,)

# Move caret one page down, extending rectangular selection to new caret position.
fun void PageDownRectExtend=2434(,)


# Move caret to top of page, or one page up if already at top of page.
fun void StutteredPageUp=2435(,)

# Move caret to top of page, or one page up if already at top of page, extending selection to new caret position.
fun void StutteredPageUpExtend=2436(,)

# Move caret to bottom of page, or one page down if already at bottom of page.
fun void StutteredPageDown=2437(,)

# Move caret to bottom of page, or one page down if already at bottom of page, extending selection to new caret position.
fun void StutteredPageDownExtend=2438(,)


# Move caret left one word, position cursor at end of word.
fun void WordLeftEnd=2439(,)

# Move caret left one word, position cursor at end of word, extending selection to new caret position.
fun void WordLeftEndExtend=2440(,)

# Move caret right one word, position cursor at end of word.
fun void WordRightEnd=2441(,)

# Move caret right one word, position cursor at end of word, extending selection to new caret position.
fun void WordRightEndExtend=2442(,)

# Set the set of characters making up whitespace for when moving or selecting by word.
# Should be called after SetWordChars.
set void SetWhitespaceChars=2443(, string characters)

# Get the set of characters making up whitespace for when moving or selecting by word.
get int GetWhitespaceChars=2647(, stringresult characters)

# Set the set of characters making up punctuation characters
# Should be called after SetWordChars.
set void SetPunctuationChars=2648(, string characters)

# Get the set of characters making up punctuation characters
get int GetPunctuationChars=2649(, stringresult characters)

# Reset the set of characters for whitespace and word characters to the defaults.
fun void SetCharsDefault=2444(,)

# Get currently selected item position in the auto-completion list
get int AutoCGetCurrent=2445(,)

# Get currently selected item text in the auto-completion list
# Returns the length of the item text
# Result is NUL-terminated.
get int AutoCGetCurrentText=2610(, stringresult text)

enu CaseInsensitiveBehaviour=SC_CASEINSENSITIVEBEHAVIOUR_
val SC_CASEINSENSITIVEBEHAVIOUR_RESPECTCASE=0
val SC_CASEINSENSITIVEBEHAVIOUR_IGNORECASE=1

ali SC_CASEINSENSITIVEBEHAVIOUR_RESPECTCASE=RESPECT_CASE
ali SC_CASEINSENSITIVEBEHAVIOUR_IGNORECASE=IGNORE_CASE

# Set auto-completion case insensitive behaviour to either prefer case-sensitive matches or have no preference.
set void AutoCSetCaseInsensitiveBehaviour=2634(CaseInsensitiveBehaviour behaviour,)

# Get auto-completion case insensitive behaviour.
get CaseInsensitiveBehaviour AutoCGetCaseInsensitiveBehaviour=2635(,)

enu MultiAutoComplete=SC_MULTIAUTOC_
val SC_MULTIAUTOC_ONCE=0
val SC_MULTIAUTOC_EACH=1

# Change the effect of autocompleting when there are multiple selections.
set void AutoCSetMulti=2636(MultiAutoComplete multi,)

# Retrieve the effect of autocompleting when there are multiple selections.
get MultiAutoComplete AutoCGetMulti=2637(,)

enu Ordering=SC_ORDER_
val SC_ORDER_PRESORTED=0
val SC_ORDER_PERFORMSORT=1
val SC_ORDER_CUSTOM=2

ali SC_ORDER_PRESORTED=PRE_SORTED
ali SC_ORDER_PERFORMSORT=PERFORM_SORT

# Set the way autocompletion lists are ordered.
set void AutoCSetOrder=2660(Ordering order,)

# Get the way autocompletion lists are ordered.
get Ordering AutoCGetOrder=2661(,)

# Enlarge the document to a particular size of text bytes.
fun void Allocate=2446(position bytes,)

# Returns the target converted to UTF8.
# Return the length in bytes.
fun position TargetAsUTF8=2447(, stringresult s)

# Set the length of the utf8 argument for calling EncodedFromUTF8.
# Set to -1 and the string will be measured to the first nul.
fun void SetLengthForEncode=2448(position bytes,)

# Translates a UTF8 string into the document encoding.
# Return the length of the result in bytes.
# On error return 0.
fun position EncodedFromUTF8=2449(string utf8, stringresult encoded)

# Find the position of a column on a line taking into account tabs and
# multi-byte characters. If beyond end of line, return line end position.
fun position FindColumn=2456(line line, position column)

enu CaretSticky=SC_CARETSTICKY_
val SC_CARETSTICKY_OFF=0
val SC_CARETSTICKY_ON=1
val SC_CARETSTICKY_WHITESPACE=2

ali SC_CARETSTICKY_WHITESPACE=WHITE_SPACE

# Can the caret preferred x position only be changed by explicit movement commands?
get CaretSticky GetCaretSticky=2457(,)

# Stop the caret preferred x position changing when the user types.
set void SetCaretSticky=2458(CaretSticky useCaretStickyBehaviour,)

# Switch between sticky and non-sticky: meant to be bound to a key.
fun void ToggleCaretSticky=2459(,)

# Enable/Disable convert-on-paste for line endings
set void SetPasteConvertEndings=2467(bool convert,)

# Get convert-on-paste setting
get bool GetPasteConvertEndings=2468(,)

# Replace the selection with text like a rectangular paste.
fun void ReplaceRectangular=2771(position length, string text)

# Duplicate the selection. If selection empty duplicate the line containing the caret.
fun void SelectionDuplicate=2469(,)

# Set background alpha of the caret line.
set void SetCaretLineBackAlpha=2470(Alpha alpha,)

# Get the background alpha of the caret line.
get Alpha GetCaretLineBackAlpha=2471(,)

enu CaretStyle=CARETSTYLE_
val CARETSTYLE_INVISIBLE=0
val CARETSTYLE_LINE=1
val CARETSTYLE_BLOCK=2
val CARETSTYLE_OVERSTRIKE_BAR=0
val CARETSTYLE_OVERSTRIKE_BLOCK=0x10
val CARETSTYLE_CURSES=0x20
val CARETSTYLE_INS_MASK=0xF
val CARETSTYLE_BLOCK_AFTER=0x100

# Set the style of the caret to be drawn.
set void SetCaretStyle=2512(CaretStyle caretStyle,)

# Returns the current style of the caret.
get CaretStyle GetCaretStyle=2513(,)

# Set the indicator used for IndicatorFillRange and IndicatorClearRange
set void SetIndicatorCurrent=2500(int indicator,)

# Get the current indicator
get int GetIndicatorCurrent=2501(,)

# Set the value used for IndicatorFillRange
set void SetIndicatorValue=2502(int value,)

# Get the current indicator value
get int GetIndicatorValue=2503(,)

# Turn a indicator on over a range.
fun void IndicatorFillRange=2504(position start, position lengthFill)

# Turn a indicator off over a range.
fun void IndicatorClearRange=2505(position start, position lengthClear)

# Are any indicators present at pos?
fun int IndicatorAllOnFor=2506(position pos,)

# What value does a particular indicator have at a position?
fun int IndicatorValueAt=2507(int indicator, position pos)

# Where does a particular indicator start?
fun position IndicatorStart=2508(int indicator, position pos)

# Where does a particular indicator end?
fun position IndicatorEnd=2509(int indicator, position pos)

# Set number of entries in position cache
set void SetPositionCache=2514(int size,)

# How many entries are allocated to the position cache?
get int GetPositionCache=2515(,)

# Set maximum number of threads used for layout
set void SetLayoutThreads=2775(int threads,)

# Get maximum number of threads used for layout
get int GetLayoutThreads=2776(,)

# Copy the selection, if selection empty copy the line with the caret
fun void CopyAllowLine=2519(,)

# Cut the selection, if selection empty cut the line with the caret
fun void CutAllowLine=2810(,)

# Set the string to separate parts when copying a multiple selection.
set void SetCopySeparator=2811(, string separator)

# Get the string to separate parts when copying a multiple selection.
get int GetCopySeparator=2812(, stringresult separator)

# Compact the document buffer and return a read-only pointer to the
# characters in the document.
get pointer GetCharacterPointer=2520(,)

# Return a read-only pointer to a range of characters in the document.
# May move the gap so that the range is contiguous, but will only move up
# to lengthRange bytes.
get pointer GetRangePointer=2643(position start, position lengthRange)

# Return a position which, to avoid performance costs, should not be within
# the range of a call to GetRangePointer.
get position GetGapPosition=2644(,)

# Set the alpha fill colour of the given indicator.
set void IndicSetAlpha=2523(int indicator, Alpha alpha)

# Get the alpha fill colour of the given indicator.
get Alpha IndicGetAlpha=2524(int indicator,)

# Set the alpha outline colour of the given indicator.
set void IndicSetOutlineAlpha=2558(int indicator, Alpha alpha)

# Get the alpha outline colour of the given indicator.
get Alpha IndicGetOutlineAlpha=2559(int indicator,)

# Set extra ascent for each line
set void SetExtraAscent=2525(int extraAscent,)

# Get extra ascent for each line
get int GetExtraAscent=2526(,)

# Set extra descent for each line
set void SetExtraDescent=2527(int extraDescent,)

# Get extra descent for each line
get int GetExtraDescent=2528(,)

# Which symbol was defined for markerNumber with MarkerDefine
fun int MarkerSymbolDefined=2529(int markerNumber,)

# Set the text in the text margin for a line
set void MarginSetText=2530(line line, string text)

# Get the text in the text margin for a line
get int MarginGetText=2531(line line, stringresult text)

# Set the style number for the text margin for a line
set void MarginSetStyle=2532(line line, int style)

# Get the style number for the text margin for a line
get int MarginGetStyle=2533(line line,)

# Set the style in the text margin for a line
set void MarginSetStyles=2534(line line, string styles)

# Get the styles in the text margin for a line
get int MarginGetStyles=2535(line line, stringresult styles)

# Clear the margin text on all lines
fun void MarginTextClearAll=2536(,)

# Get the start of the range of style numbers used for margin text
set void MarginSetStyleOffset=2537(int style,)

# Get the start of the range of style numbers used for margin text
get int MarginGetStyleOffset=2538(,)

enu MarginOption=SC_MARGINOPTION_
val SC_MARGINOPTION_NONE=0
val SC_MARGINOPTION_SUBLINESELECT=1

ali SC_MARGINOPTION_SUBLINESELECT=SUB_LINE_SELECT

# Set the margin options.
set void SetMarginOptions=2539(MarginOption marginOptions,)

# Get the margin options.
get MarginOption GetMarginOptions=2557(,)

# Set the annotation text for a line
set void AnnotationSetText=2540(line line, string text)

# Get the annotation text for a line
get int AnnotationGetText=2541(line line, stringresult text)

# Set the style number for the annotations for a line
set void AnnotationSetStyle=2542(line line, int style)

# Get the style number for the annotations for a line
get int AnnotationGetStyle=2543(line line,)

# Set the annotation styles for a line
set void AnnotationSetStyles=2544(line line, string styles)

# Get the annotation styles for a line
get int AnnotationGetStyles=2545(line line, stringresult styles)

# Get the number of annotation lines for a line
get int AnnotationGetLines=2546(line line,)

# Clear the annotations from all lines
fun void AnnotationClearAll=2547(,)

enu AnnotationVisible=ANNOTATION_
val ANNOTATION_HIDDEN=0
val ANNOTATION_STANDARD=1
val ANNOTATION_BOXED=2
val ANNOTATION_INDENTED=3

# Set the visibility for the annotations for a view
set void AnnotationSetVisible=2548(AnnotationVisible visible,)

# Get the visibility for the annotations for a view
get AnnotationVisible AnnotationGetVisible=2549(,)

# Get the start of the range of style numbers used for annotations
set void AnnotationSetStyleOffset=2550(int style,)

# Get the start of the range of style numbers used for annotations
get int AnnotationGetStyleOffset=2551(,)

# Release all extended (>255) style numbers
fun void ReleaseAllExtendedStyles=2552(,)

# Allocate some extended (>255) style numbers and return the start of the range
fun int AllocateExtendedStyles=2553(int numberStyles,)

enu UndoFlags=UNDO_
val UNDO_NONE=0
val UNDO_MAY_COALESCE=1

# Add a container action to the undo stack
fun void AddUndoAction=2560(int token, UndoFlags flags)

# Find the position of a character from a point within the window.
fun position CharPositionFromPoint=2561(int x, int y)

# Find the position of a character from a point within the window.
# Return INVALID_POSITION if not close to text.
fun position CharPositionFromPointClose=2562(int x, int y)

# Set whether switching to rectangular mode while selecting with the mouse is allowed.
set void SetMouseSelectionRectangularSwitch=2668(bool mouseSelectionRectangularSwitch,)

# Whether switching to rectangular mode while selecting with the mouse is allowed.
get bool GetMouseSelectionRectangularSwitch=2669(,)

# Set whether multiple selections can be made
set void SetMultipleSelection=2563(bool multipleSelection,)

# Whether multiple selections can be made
get bool GetMultipleSelection=2564(,)

# Set whether typing can be performed into multiple selections
set void SetAdditionalSelectionTyping=2565(bool additionalSelectionTyping,)

# Whether typing can be performed into multiple selections
get bool GetAdditionalSelectionTyping=2566(,)

# Set whether additional carets will blink
set void SetAdditionalCaretsBlink=2567(bool additionalCaretsBlink,)

# Whether additional carets will blink
get bool GetAdditionalCaretsBlink=2568(,)

# Set whether additional carets are visible
set void SetAdditionalCaretsVisible=2608(bool additionalCaretsVisible,)

# Whether additional carets are visible
get bool GetAdditionalCaretsVisible=2609(,)

# How many selections are there?
get int GetSelections=2570(,)

# Is every selected range empty?
get bool GetSelectionEmpty=2650(,)

# Clear selections to a single empty stream selection
fun void ClearSelections=2571(,)

# Set a simple selection
fun void SetSelection=2572(position caret, position anchor)

# Add a selection
fun void AddSelection=2573(position caret, position anchor)

# Find the selection index for a point. -1 when not at a selection.
fun int SelectionFromPoint=2474(int x, int y)

# Drop one selection
fun void DropSelectionN=2671(int selection,)

# Set the main selection
set void SetMainSelection=2574(int selection,)

# Which selection is the main selection
get int GetMainSelection=2575(,)

# Set the caret position of the nth selection.
set void SetSelectionNCaret=2576(int selection, position caret)

# Return the caret position of the nth selection.
get position GetSelectionNCaret=2577(int selection,)

# Set the anchor position of the nth selection.
set void SetSelectionNAnchor=2578(int selection, position anchor)

# Return the anchor position of the nth selection.
get position GetSelectionNAnchor=2579(int selection,)

# Set the virtual space of the caret of the nth selection.
set void SetSelectionNCaretVirtualSpace=2580(int selection, position space)

# Return the virtual space of the caret of the nth selection.
get position GetSelectionNCaretVirtualSpace=2581(int selection,)

# Set the virtual space of the anchor of the nth selection.
set void SetSelectionNAnchorVirtualSpace=2582(int selection, position space)

# Return the virtual space of the anchor of the nth selection.
get position GetSelectionNAnchorVirtualSpace=2583(int selection,)

# Sets the position that starts the selection - this becomes the anchor.
set void SetSelectionNStart=2584(int selection, position anchor)

# Returns the position at the start of the selection.
get position GetSelectionNStart=2585(int selection,)

# Returns the virtual space at the start of the selection.
get position GetSelectionNStartVirtualSpace=2726(int selection,)

# Sets the position that ends the selection - this becomes the currentPosition.
set void SetSelectionNEnd=2586(int selection, position caret)

# Returns the virtual space at the end of the selection.
get position GetSelectionNEndVirtualSpace=2727(int selection,)

# Returns the position at the end of the selection.
get position GetSelectionNEnd=2587(int selection,)

# Set the caret position of the rectangular selection.
set void SetRectangularSelectionCaret=2588(position caret,)

# Return the caret position of the rectangular selection.
get position GetRectangularSelectionCaret=2589(,)

# Set the anchor position of the rectangular selection.
set void SetRectangularSelectionAnchor=2590(position anchor,)

# Return the anchor position of the rectangular selection.
get position GetRectangularSelectionAnchor=2591(,)

# Set the virtual space of the caret of the rectangular selection.
set void SetRectangularSelectionCaretVirtualSpace=2592(position space,)

# Return the virtual space of the caret of the rectangular selection.
get position GetRectangularSelectionCaretVirtualSpace=2593(,)

# Set the virtual space of the anchor of the rectangular selection.
set void SetRectangularSelectionAnchorVirtualSpace=2594(position space,)

# Return the virtual space of the anchor of the rectangular selection.
get position GetRectangularSelectionAnchorVirtualSpace=2595(,)

enu VirtualSpace=SCVS_
val SCVS_NONE=0
val SCVS_RECTANGULARSELECTION=1
val SCVS_USERACCESSIBLE=2
val SCVS_NOWRAPLINESTART=4

ali SCVS_RECTANGULARSELECTION=RECTANGULAR_SELECTION
ali SCVS_USERACCESSIBLE=USER_ACCESSIBLE
ali SCVS_NOWRAPLINESTART=NO_WRAP_LINE_START

# Set options for virtual space behaviour.
set void SetVirtualSpaceOptions=2596(VirtualSpace virtualSpaceOptions,)

# Return options for virtual space behaviour.
get VirtualSpace GetVirtualSpaceOptions=2597(,)

# On GTK, allow selecting the modifier key to use for mouse-based
# rectangular selection. Often the window manager requires Alt+Mouse Drag
# for moving windows.
# Valid values are SCMOD_CTRL(default), SCMOD_ALT, or SCMOD_SUPER.

set void SetRectangularSelectionModifier=2598(int modifier,)

# Get the modifier key used for rectangular selection.
get int GetRectangularSelectionModifier=2599(,)

# Set the foreground colour of additional selections.
# Must have previously called SetSelFore with non-zero first argument for this to have an effect.
set void SetAdditionalSelFore=2600(colour fore,)

# Set the background colour of additional selections.
# Must have previously called SetSelBack with non-zero first argument for this to have an effect.
set void SetAdditionalSelBack=2601(colour back,)

# Set the alpha of the selection.
set void SetAdditionalSelAlpha=2602(Alpha alpha,)

# Get the alpha of the selection.
get Alpha GetAdditionalSelAlpha=2603(,)

# Set the foreground colour of additional carets.
set void SetAdditionalCaretFore=2604(colour fore,)

# Get the foreground colour of additional carets.
get colour GetAdditionalCaretFore=2605(,)

# Set the main selection to the next selection.
fun void RotateSelection=2606(,)

# Swap that caret and anchor of the main selection.
fun void SwapMainAnchorCaret=2607(,)

# Add the next occurrence of the main selection to the set of selections as main.
# If the current selection is empty then select word around caret.
fun void MultipleSelectAddNext=2688(,)

# Add each occurrence of the main selection in the target to the set of selections.
# If the current selection is empty then select word around caret.
fun void MultipleSelectAddEach=2689(,)

# Indicate that the internal state of a lexer has changed over a range and therefore
# there may be a need to redraw.
fun int ChangeLexerState=2617(position start, position end)

# Find the next line at or after lineStart that is a contracted fold header line.
# Return -1 when no more lines.
fun line ContractedFoldNext=2618(line lineStart,)

# Centre current line in window.
fun void VerticalCentreCaret=2619(,)

# Move the selected lines up one line, shifting the line above after the selection
fun void MoveSelectedLinesUp=2620(,)

# Move the selected lines down one line, shifting the line below before the selection
fun void MoveSelectedLinesDown=2621(,)

# Set the identifier reported as idFrom in notification messages.
set void SetIdentifier=2622(int identifier,)

# Get the identifier.
get int GetIdentifier=2623(,)

# Set the width for future RGBA image data.
set void RGBAImageSetWidth=2624(int width,)

# Set the height for future RGBA image data.
set void RGBAImageSetHeight=2625(int height,)

# Set the scale factor in percent for future RGBA image data.
set void RGBAImageSetScale=2651(int scalePercent,)

# Define a marker from RGBA data.
# It has the width and height from RGBAImageSetWidth/Height
fun void MarkerDefineRGBAImage=2626(int markerNumber, string pixels)

# Register an RGBA image for use in autocompletion lists.
# It has the width and height from RGBAImageSetWidth/Height
fun void RegisterRGBAImage=2627(int type, string pixels)

# Scroll to start of document.
fun void ScrollToStart=2628(,)

# Scroll to end of document.
fun void ScrollToEnd=2629(,)

enu Technology=SC_TECHNOLOGY_
val SC_TECHNOLOGY_DEFAULT=0
val SC_TECHNOLOGY_DIRECTWRITE=1
val SC_TECHNOLOGY_DIRECTWRITERETAIN=2
val SC_TECHNOLOGY_DIRECTWRITEDC=3
val SC_TECHNOLOGY_DIRECT_WRITE_1=4

ali SC_TECHNOLOGY_DIRECTWRITE=DIRECT_WRITE
ali SC_TECHNOLOGY_DIRECTWRITERETAIN=DIRECT_WRITE_RETAIN
ali SC_TECHNOLOGY_DIRECTWRITEDC=DIRECT_WRITE_D_C

# Set the technology used.
set void SetTechnology=2630(Technology technology,)

# Get the tech.
get Technology GetTechnology=2631(,)

# Create an ILoader*.
fun pointer CreateLoader=2632(position bytes, DocumentOption documentOptions)

# On macOS, show a find indicator.
fun void FindIndicatorShow=2640(position start, position end)

# On macOS, flash a find indicator, then fade out.
fun void FindIndicatorFlash=2641(position start, position end)

# On macOS, hide the find indicator.
fun void FindIndicatorHide=2642(,)

# Move caret to before first visible character on display line.
# If already there move to first character on display line.
fun void VCHomeDisplay=2652(,)

# Like VCHomeDisplay but extending selection to new caret position.
fun void VCHomeDisplayExtend=2653(,)

# Is the caret line always visible?
get bool GetCaretLineVisibleAlways=2654(,)

# Sets the caret line to always visible.
set void SetCaretLineVisibleAlways=2655(bool alwaysVisible,)

# Line end types which may be used in addition to LF, CR, and CRLF
# SC_LINE_END_TYPE_UNICODE includes U+2028 Line Separator,
# U+2029 Paragraph Separator, and U+0085 Next Line
enu LineEndType=SC_LINE_END_TYPE_
val SC_LINE_END_TYPE_DEFAULT=0
val SC_LINE_END_TYPE_UNICODE=1

# Set the line end types that the application wants to use. May not be used if incompatible with lexer or encoding.
set void SetLineEndTypesAllowed=2656(LineEndType lineEndBitSet,)

# Get the line end types currently allowed.
get LineEndType GetLineEndTypesAllowed=2657(,)

# Get the line end types currently recognised. May be a subset of the allowed types due to lexer limitation.
get LineEndType GetLineEndTypesActive=2658(,)

# Set the way a character is drawn.
set void SetRepresentation=2665(string encodedCharacter, string representation)

# Get the way a character is drawn.
# Result is NUL-terminated.
get int GetRepresentation=2666(string encodedCharacter, stringresult representation)

# Remove a character representation.
fun void ClearRepresentation=2667(string encodedCharacter,)

# Clear representations to default.
fun void ClearAllRepresentations=2770(,)

# Can draw representations in various ways
enu RepresentationAppearance=SC_REPRESENTATION
val SC_REPRESENTATION_PLAIN=0
val SC_REPRESENTATION_BLOB=1
val SC_REPRESENTATION_COLOUR=0x10

# Set the appearance of a representation.
set void SetRepresentationAppearance=2766(string encodedCharacter, RepresentationAppearance appearance)

# Get the appearance of a representation.
get RepresentationAppearance GetRepresentationAppearance=2767(string encodedCharacter,)

# Set the colour of a representation.
set void SetRepresentationColour=2768(string encodedCharacter, colouralpha colour)

# Get the colour of a representation.
get colouralpha GetRepresentationColour=2769(string encodedCharacter,)

# Set the end of line annotation text for a line
set void EOLAnnotationSetText=2740(line line, string text)

# Get the end of line annotation text for a line
get int EOLAnnotationGetText=2741(line line, stringresult text)

# Set the style number for the end of line annotations for a line
set void EOLAnnotationSetStyle=2742(line line, int style)

# Get the style number for the end of line annotations for a line
get int EOLAnnotationGetStyle=2743(line line,)

# Clear the end of annotations from all lines
fun void EOLAnnotationClearAll=2744(,)

enu EOLAnnotationVisible=EOLANNOTATION_
val EOLANNOTATION_HIDDEN=0x0
val EOLANNOTATION_STANDARD=0x1
val EOLANNOTATION_BOXED=0x2
val EOLANNOTATION_STADIUM=0x100
val EOLANNOTATION_FLAT_CIRCLE=0x101
val EOLANNOTATION_ANGLE_CIRCLE=0x102
val EOLANNOTATION_CIRCLE_FLAT=0x110
val EOLANNOTATION_FLATS=0x111
val EOLANNOTATION_ANGLE_FLAT=0x112
val EOLANNOTATION_CIRCLE_ANGLE=0x120
val EOLANNOTATION_FLAT_ANGLE=0x121
val EOLANNOTATION_ANGLES=0x122

# Set the visibility for the end of line annotations for a view
set void EOLAnnotationSetVisible=2745(EOLAnnotationVisible visible,)

# Get the visibility for the end of line annotations for a view
get EOLAnnotationVisible EOLAnnotationGetVisible=2746(,)

# Get the start of the range of style numbers used for end of line annotations
set void EOLAnnotationSetStyleOffset=2747(int style,)

# Get the start of the range of style numbers used for end of line annotations
get int EOLAnnotationGetStyleOffset=2748(,)

enu Supports=SC_SUPPORTS_
val SC_SUPPORTS_LINE_DRAWS_FINAL=0
val SC_SUPPORTS_PIXEL_DIVISIONS=1
val SC_SUPPORTS_FRACTIONAL_STROKE_WIDTH=2
val SC_SUPPORTS_TRANSLUCENT_STROKE=3
val SC_SUPPORTS_PIXEL_MODIFICATION=4
val SC_SUPPORTS_THREAD_SAFE_MEASURE_WIDTHS=5

# Get whether a feature is supported
get bool SupportsFeature=2750(Supports feature,)

enu LineCharacterIndexType=SC_LINECHARACTERINDEX_
val SC_LINECHARACTERINDEX_NONE=0
val SC_LINECHARACTERINDEX_UTF32=1
val SC_LINECHARACTERINDEX_UTF16=2

# Retrieve line character index state.
get LineCharacterIndexType GetLineCharacterIndex=2710(,)

# Request line character index be created or its use count increased.
fun void AllocateLineCharacterIndex=2711(LineCharacterIndexType lineCharacterIndex,)

# Decrease use count of line character index and remove if 0.
fun void ReleaseLineCharacterIndex=2712(LineCharacterIndexType lineCharacterIndex,)

# Retrieve the document line containing a position measured in index units.
fun line LineFromIndexPosition=2713(position pos, LineCharacterIndexType lineCharacterIndex)

# Retrieve the position measured in index units at the start of a document line.
fun position IndexPositionFromLine=2714(line line, LineCharacterIndexType lineCharacterIndex)

# Start notifying the container of all key presses and commands.
fun void StartRecord=3001(,)

# Stop notifying the container of all key presses and commands.
fun void StopRecord=3002(,)

# Retrieve the lexing language of the document.
get int GetLexer=4002(,)

# Colourise a segment of the document using the current lexing language.
fun void Colourise=4003(position start, position end)

# Set up a value that may be used by a lexer for some optional feature.
set void SetProperty=4004(string key, string value)

# Maximum value of keywordSet parameter of SetKeyWords.
val KEYWORDSET_MAX=8

# Set up the key words used by the lexer.
set void SetKeyWords=4005(int keyWordSet, string keyWords)

# Retrieve a "property" value previously set with SetProperty.
# Result is NUL-terminated.
get int GetProperty=4008(string key, stringresult value)

# Retrieve a "property" value previously set with SetProperty,
# with "$()" variable replacement on returned buffer.
# Result is NUL-terminated.
get int GetPropertyExpanded=4009(string key, stringresult value)

# Retrieve a "property" value previously set with SetProperty,
# interpreted as an int AFTER any "$()" variable replacement.
get int GetPropertyInt=4010(string key, int defaultValue)

# Retrieve the name of the lexer.
# Return the length of the text.
# Result is NUL-terminated.
get int GetLexerLanguage=4012(, stringresult language)

# For private communication between an application and a known lexer.
fun pointer PrivateLexerCall=4013(int operation, pointer pointer)

# Retrieve a '\n' separated list of properties understood by the current lexer.
# Result is NUL-terminated.
fun int PropertyNames=4014(, stringresult names)

enu TypeProperty=SC_TYPE_
val SC_TYPE_BOOLEAN=0
val SC_TYPE_INTEGER=1
val SC_TYPE_STRING=2

# Retrieve the type of a property.
fun TypeProperty PropertyType=4015(string name,)

# Describe a property.
# Result is NUL-terminated.
fun int DescribeProperty=4016(string name, stringresult description)

# Retrieve a '\n' separated list of descriptions of the keyword sets understood by the current lexer.
# Result is NUL-terminated.
fun int DescribeKeyWordSets=4017(, stringresult descriptions)

# Bit set of LineEndType enumertion for which line ends beyond the standard
# LF, CR, and CRLF are supported by the lexer.
get LineEndType GetLineEndTypesSupported=4018(,)

# Allocate a set of sub styles for a particular base style, returning start of range
fun int AllocateSubStyles=4020(int styleBase, int numberStyles)

# The starting style number for the sub styles associated with a base style
get int GetSubStylesStart=4021(int styleBase,)

# The number of sub styles associated with a base style
get int GetSubStylesLength=4022(int styleBase,)

# For a sub style, return the base style, else return the argument.
get int GetStyleFromSubStyle=4027(int subStyle,)

# For a secondary style, return the primary style, else return the argument.
get int GetPrimaryStyleFromStyle=4028(int style,)

# Free allocated sub styles
fun void FreeSubStyles=4023(,)

# Set the identifiers that are shown in a particular style
set void SetIdentifiers=4024(int style, string identifiers)

# Where styles are duplicated by a feature such as active/inactive code
# return the distance between the two types.
get int DistanceToSecondaryStyles=4025(,)

# Get the set of base styles that can be extended with sub styles
# Result is NUL-terminated.
get int GetSubStyleBases=4026(, stringresult styles)

# Retrieve the number of named styles for the lexer.
get int GetNamedStyles=4029(,)

# Retrieve the name of a style.
# Result is NUL-terminated.
fun int NameOfStyle=4030(int style, stringresult name)

# Retrieve a ' ' separated list of style tags like "literal quoted string".
# Result is NUL-terminated.
fun int TagsOfStyle=4031(int style, stringresult tags)

# Retrieve a description of a style.
# Result is NUL-terminated.
fun int DescriptionOfStyle=4032(int style, stringresult description)

# Set the lexer from an ILexer*.
set void SetILexer=4033(, pointer ilexer)

# Notifications
# Type of modification and the action which caused the modification.
# These are defined as a bit mask to make it easy to specify which notifications are wanted.
# One bit is set from each of SC_MOD_* and SC_PERFORMED_*.
enu ModificationFlags=SC_MOD_ SC_PERFORMED_ SC_MULTISTEPUNDOREDO SC_LASTSTEPINUNDOREDO SC_MULTILINEUNDOREDO SC_STARTACTION SC_MODEVENTMASKALL
val SC_MOD_NONE=0x0
val SC_MOD_INSERTTEXT=0x1
val SC_MOD_DELETETEXT=0x2
val SC_MOD_CHANGESTYLE=0x4
val SC_MOD_CHANGEFOLD=0x8
val SC_PERFORMED_USER=0x10
val SC_PERFORMED_UNDO=0x20
val SC_PERFORMED_REDO=0x40
val SC_MULTISTEPUNDOREDO=0x80
val SC_LASTSTEPINUNDOREDO=0x100
val SC_MOD_CHANGEMARKER=0x200
val SC_MOD_BEFOREINSERT=0x400
val SC_MOD_BEFOREDELETE=0x800
val SC_MULTILINEUNDOREDO=0x1000
val SC_STARTACTION=0x2000
val SC_MOD_CHANGEINDICATOR=0x4000
val SC_MOD_CHANGELINESTATE=0x8000
val SC_MOD_CHANGEMARGIN=0x10000
val SC_MOD_CHANGEANNOTATION=0x20000
val SC_MOD_CONTAINER=0x40000
val SC_MOD_LEXERSTATE=0x80000
val SC_MOD_INSERTCHECK=0x100000
val SC_MOD_CHANGETABSTOPS=0x200000
val SC_MOD_CHANGEEOLANNOTATION=0x400000
val SC_MODEVENTMASKALL=0x7FFFFF

ali SC_MOD_INSERTTEXT=INSERT_TEXT
ali SC_MOD_DELETETEXT=DELETE_TEXT
ali SC_MOD_CHANGESTYLE=CHANGE_STYLE
ali SC_MOD_CHANGEFOLD=CHANGE_FOLD
ali SC_MULTISTEPUNDOREDO=MULTI_STEP_UNDO_REDO
ali SC_LASTSTEPINUNDOREDO=LAST_STEP_IN_UNDO_REDO
ali SC_MOD_CHANGEMARKER=CHANGE_MARKER
ali SC_MOD_BEFOREINSERT=BEFORE_INSERT
ali SC_MOD_BEFOREDELETE=BEFORE_DELETE
ali SC_MULTILINEUNDOREDO=MULTILINE_UNDO_REDO
ali SC_STARTACTION=START_ACTION
ali SC_MOD_CHANGEINDICATOR=CHANGE_INDICATOR
ali SC_MOD_CHANGELINESTATE=CHANGE_LINE_STATE
ali SC_MOD_CHANGEMARGIN=CHANGE_MARGIN
ali SC_MOD_CHANGEANNOTATION=CHANGE_ANNOTATION
ali SC_MOD_LEXERSTATE=LEXER_STATE
ali SC_MOD_INSERTCHECK=INSERT_CHECK
ali SC_MOD_CHANGETABSTOPS=CHANGE_TAB_STOPS
ali SC_MOD_CHANGEEOLANNOTATION=CHANGE_E_O_L_ANNOTATION
ali SC_MODEVENTMASKALL=EVENT_MASK_ALL

enu Update=SC_UPDATE_
val SC_UPDATE_NONE=0x0
val SC_UPDATE_CONTENT=0x1
val SC_UPDATE_SELECTION=0x2
val SC_UPDATE_V_SCROLL=0x4
val SC_UPDATE_H_SCROLL=0x8

# For compatibility, these go through the COMMAND notification rather than NOTIFY
# and should have had exactly the same values as the EN_* constants.
# Unfortunately the SETFOCUS and KILLFOCUS are flipped over from EN_*
# As clients depend on these constants, this will not be changed.
enu FocusChange=SCEN_
val SCEN_CHANGE=768
val SCEN_SETFOCUS=512
val SCEN_KILLFOCUS=256

# Symbolic key codes and modifier flags.
# ASCII and other printable characters below 256.
# Extended keys above 300.

enu Keys=SCK_
val SCK_DOWN=300
val SCK_UP=301
val SCK_LEFT=302
val SCK_RIGHT=303
val SCK_HOME=304
val SCK_END=305
val SCK_PRIOR=306
val SCK_NEXT=307
val SCK_DELETE=308
val SCK_INSERT=309
val SCK_ESCAPE=7
val SCK_BACK=8
val SCK_TAB=9
val SCK_RETURN=13
val SCK_ADD=310
val SCK_SUBTRACT=311
val SCK_DIVIDE=312
val SCK_WIN=313
val SCK_RWIN=314
val SCK_MENU=315

ali SCK_RWIN=R_WIN

enu KeyMod=SCMOD_
val SCMOD_NORM=0
val SCMOD_SHIFT=1
val SCMOD_CTRL=2
val SCMOD_ALT=4
val SCMOD_SUPER=8
val SCMOD_META=16

enu CompletionMethods=SC_AC_
val SC_AC_FILLUP=1
val SC_AC_DOUBLECLICK=2
val SC_AC_TAB=3
val SC_AC_NEWLINE=4
val SC_AC_COMMAND=5
val SC_AC_SINGLE_CHOICE=6

ali SC_AC_FILLUP=FILL_UP
ali SC_AC_DOUBLECLICK=DOUBLE_CLICK

# characterSource for SCN_CHARADDED
enu CharacterSource=SC_CHARACTERSOURCE_
# Direct input characters.
val SC_CHARACTERSOURCE_DIRECT_INPUT=0
# IME (inline mode) or dead key tentative input characters.
val SC_CHARACTERSOURCE_TENTATIVE_INPUT=1
# IME (either inline or windowed mode) full composited string.
val SC_CHARACTERSOURCE_IME_RESULT=2

# Events

evt void StyleNeeded=2000(int position)
evt void CharAdded=2001(int ch, int characterSource)
evt void SavePointReached=2002(void)
evt void SavePointLeft=2003(void)
evt void ModifyAttemptRO=2004(void)
# GTK Specific to work around focus and accelerator problems:
evt void Key=2005(int ch, int modifiers)
evt void DoubleClick=2006(int modifiers, int position, int line)
evt void UpdateUI=2007(int updated)
evt void Modified=2008(int position, int modificationType, string text, int length, int linesAdded, int line, int foldLevelNow, int foldLevelPrev, int token, int annotationLinesAdded)
evt void MacroRecord=2009(int message, int wParam, int lParam)
evt void MarginClick=2010(int modifiers, int position, int margin)
evt void NeedShown=2011(int position, int length)
evt void Painted=2013(void)
evt void UserListSelection=2014(int listType, string text, int position, int ch, CompletionMethods listCompletionMethod)
evt void URIDropped=2015(string text)
evt void DwellStart=2016(int position, int x, int y)
evt void DwellEnd=2017(int position, int x, int y)
evt void Zoom=2018(void)
evt void HotSpotClick=2019(int modifiers, int position)
evt void HotSpotDoubleClick=2020(int modifiers, int position)
evt void CallTipClick=2021(int position)
evt void AutoCSelection=2022(string text, int position, int ch, CompletionMethods listCompletionMethod)
evt void IndicatorClick=2023(int modifiers, int position)
evt void IndicatorRelease=2024(int modifiers, int position)
evt void AutoCCancelled=2025(void)
evt void AutoCCharDeleted=2026(void)
evt void HotSpotReleaseClick=2027(int modifiers, int position)
evt void FocusIn=2028(void)
evt void FocusOut=2029(void)
evt void AutoCCompleted=2030(string text, int position, int ch, CompletionMethods listCompletionMethod)
evt void MarginRightClick=2031(int modifiers, int position, int margin)
evt void AutoCSelectionChange=2032(int listType, string text, int position)

cat Provisional

enu Bidirectional=SC_BIDIRECTIONAL_
val SC_BIDIRECTIONAL_DISABLED=0
val SC_BIDIRECTIONAL_L2R=1
val SC_BIDIRECTIONAL_R2L=2

# Retrieve bidirectional text display state.
get Bidirectional GetBidirectional=2708(,)

# Set bidirectional text display state.
set void SetBidirectional=2709(Bidirectional bidirectional,)

cat Deprecated

# Divide each styling byte into lexical class bits (default: 5) and indicator
# bits (default: 3). If a lexer requires more than 32 lexical states, then this
# is used to expand the possible states.
set void SetStyleBits=2090(int bits,)

# Retrieve number of bits in style bytes used to hold the lexical state.
get int GetStyleBits=2091(,)

# Retrieve the number of bits the current lexer needs for styling.
get int GetStyleBitsNeeded=4011(,)

# Deprecated in 3.5.5

# Always interpret keyboard input as Unicode
set void SetKeysUnicode=2521(bool keysUnicode,)

# Are keys always interpreted as Unicode?
get bool GetKeysUnicode=2522(,)

# Is drawing done in two phases with backgrounds drawn before foregrounds?
get bool GetTwoPhaseDraw=2283(,)

# In twoPhaseDraw mode, drawing is performed in two phases, first the background
# and then the foreground. This avoids chopping off characters that overlap the next run.
set void SetTwoPhaseDraw=2284(bool twoPhase,)

val INDIC0_MASK=0x20
val INDIC1_MASK=0x40
val INDIC2_MASK=0x80
val INDICS_MASK=0xE0
