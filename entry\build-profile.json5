{
  "apiType": "stageMode",
    "buildOption": {
      "externalNativeOptions": {
        "path": "./src/main/cpp/CMakeLists.txt",
        "arguments": "",
        "cppFlags": "",
      }
    },
    "buildOptionSet": [
      {
        "name": "release",
        "arkOptions": {
          "obfuscation": {
            "ruleOptions": {
              "enable": false,
              "files": [
                "./obfuscation-rules.txt"
              ]
            }
          }
        },
        "nativeLib": {
          "debugSymbol": {
            "strip": true,
            "exclude": []
          }
        }
      },
    ],
    "targets": [
      {
        "name": "default"
      },
      {
        "name": "ohosTest",
      }
    ],
}